@extends('backend.layouts.master')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Environment Management</h6>
        <a href="{{ route('environments.create') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Add Environment">
            <i class="fas fa-plus"></i> Add Environment
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if($environments->count() > 0)
                <table class="table table-bordered" id="environment-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Name</th>
                            <th>Slug</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Default</th>
                            <th>Created Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($environments as $environment)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>
                                <strong>{{ $environment->name }}</strong>
                                @if($environment->is_default)
                                    <span class="badge badge-info badge-sm ml-1">Default</span>
                                @endif
                            </td>
                            <td><code>{{ $environment->slug }}</code></td>
                            <td>{{ Str::limit($environment->description, 50) ?? 'No description' }}</td>
                            <td>
                                <span class="badge {{ $environment->status_badge }}">
                                    {{ $environment->status_text }}
                                </span>
                            </td>
                            <td>
                                @if($environment->is_default)
                                    <i class="fas fa-check-circle text-success" title="Default Environment"></i>
                                @else
                                    <button class="btn btn-sm btn-outline-info set-default-btn" 
                                            data-id="{{ $environment->id }}" 
                                            data-name="{{ $environment->name }}"
                                            title="Set as Default">
                                        <i class="fas fa-star"></i>
                                    </button>
                                @endif
                            </td>
                            <td>{{ $environment->created_at->format('M d, Y') }}</td>
                            <td>
                                <a href="{{ route('environments.show', $environment->id) }}" class="btn btn-warning btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="View" data-placement="bottom">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('environments.edit', $environment->id) }}" class="btn btn-primary btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="Edit" data-placement="bottom">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm float-left mr-1 toggle-status-btn {{ $environment->is_active ? 'btn-success' : 'btn-secondary' }}" 
                                        style="height:30px; width:30px;border-radius:50%" 
                                        data-id="{{ $environment->id }}" 
                                        data-status="{{ $environment->is_active }}"
                                        data-toggle="tooltip" 
                                        title="{{ $environment->is_active ? 'Deactivate' : 'Activate' }}" 
                                        data-placement="bottom">
                                    <i class="fas {{ $environment->is_active ? 'fa-toggle-on' : 'fa-toggle-off' }}"></i>
                                </button>
                                @if(!$environment->is_default)
                                <form method="POST" action="{{ route('environments.destroy', [$environment->id]) }}" style="display:inline">
                                    @csrf 
                                    @method('delete')
                                    <button class="btn btn-danger btn-sm dltBtn" data-id="{{ $environment->id }}" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" data-placement="bottom" title="Delete">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <h6 class="text-center">No environments found! Please create an environment.</h6>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
<style>
    div.dataTables_wrapper div.dataTables_paginate {
        margin: 0;
        white-space: nowrap;
        text-align: right;
    }
    
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 2px 0;
        white-space: nowrap;
        justify-content: flex-end;
    }
    
    .dataTables_length, .dataTables_filter, .dataTables_info, .dataTables_paginate {
        margin-top: 12px;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{ asset('backend/vendor/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>

<!-- Page level custom scripts -->
<script src="{{ asset('backend/js/demo/datatables-demo.js') }}"></script>
<script>
    $('#environment-dataTable').DataTable({
        "columnDefs": [
            {
                "orderable": false,
                "targets": [7]
            }
        ]
    });

    // Delete confirmation
    $('.dltBtn').click(function(e) {
        var form = $(this).closest('form');
        var dataID = $(this).data('id');
        e.preventDefault();
        swal({
            title: "Are you sure?",
            text: "Once deleted, you will not be able to recover this environment!",
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                form.submit();
            }
        });
    });

    // Toggle status
    $('.toggle-status-btn').click(function() {
        var envId = $(this).data('id');
        var currentStatus = $(this).data('status');
        var btn = $(this);
        
        $.ajax({
            url: '/admin/environments/' + envId + '/toggle-status',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to toggle environment status');
            }
        });
    });

    // Set as default
    $('.set-default-btn').click(function() {
        var envId = $(this).data('id');
        var envName = $(this).data('name');
        
        swal({
            title: "Set as Default?",
            text: "Are you sure you want to set '" + envName + "' as the default environment?",
            icon: "info",
            buttons: true,
        })
        .then((willSet) => {
            if (willSet) {
                $.ajax({
                    url: '/admin/environments/' + envId + '/set-default',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Failed to set default environment');
                    }
                });
            }
        });
    });
</script>
@endpush
