@extends('backend.layouts.master')

@section('title','Subscriptions - DynamoDB')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Subscriptions ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($subscriptions) > 0)
                <table class="table table-bordered" id="subscription-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Plan Name</th>
                            <th>Status</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($subscriptions as $index => $subscription)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($subscription['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $subscription['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <strong>{{ $subscription['planName'] ?? 'N/A' }}</strong>
                            </td>
                            <td>
                                @if(isset($subscription['status']))
                                    @switch($subscription['status'])
                                        @case('ACTIVE')
                                            <span class="badge badge-success">{{ $subscription['status'] }}</span>
                                            @break
                                        @case('INACTIVE')
                                            <span class="badge badge-danger">{{ $subscription['status'] }}</span>
                                            @break
                                        @case('EXPIRED')
                                            <span class="badge badge-warning">{{ $subscription['status'] }}</span>
                                            @break
                                        @case('CANCELLED')
                                            <span class="badge badge-secondary">{{ $subscription['status'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $subscription['status'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['startDate'] ?? null) }}
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['endDate'] ?? null) }}
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['createdAt'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No subscriptions found!</h6>
                    <p>There are no subscriptions in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#subscription-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 7, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });
    });
</script>
@endpush
