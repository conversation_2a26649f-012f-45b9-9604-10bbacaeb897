<?php

namespace App\Services;

use Aws\DynamoDb\DynamoDbClient;
use Aws\DynamoDb\Exception\DynamoDbException;
use Illuminate\Support\Facades\Log;

class DynamoDBService
{
    protected $dynamoDb;
    protected $region;

    public function __construct()
    {
        $this->region = env('AWS_DYNAMODB_REGION', 'us-east-1');

        $this->dynamoDb = new DynamoDbClient([
            'region' => $this->region,
            'version' => 'latest',
            'credentials' => [
                'key' => env('AWS_ACCESS_KEY_ID'),
                'secret' => env('AWS_SECRET_ACCESS_KEY'),
            ],
            'suppress_php_deprecation_warning' => env('AWS_SUPPRESS_PHP_DEPRECATION_WARNING', true),
        ]);
    }

    /**
     * List all tables in DynamoDB
     */
    public function listTables()
    {
        try {
            $result = $this->dynamoDb->listTables();
            return [
                'success' => true,
                'data' => $result['TableNames'],
                'message' => 'Tables retrieved successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB List Tables Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => [],
                'message' => 'Error retrieving tables: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Scan a table to get all items (use with caution on large tables)
     */
    public function scanTable($tableName, $limit = 100)
    {
        try {
            $params = [
                'TableName' => $tableName,
                'Limit' => $limit
            ];

            $result = $this->dynamoDb->scan($params);

            // Unmarshall the items for easier use
            $items = [];
            foreach ($result['Items'] as $item) {
                $items[] = $this->unmarshallItem($item);
            }

            return [
                'success' => true,
                'data' => $items,
                'count' => $result['Count'],
                'scanned_count' => $result['ScannedCount'],
                'message' => 'Items retrieved successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Scan Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => [],
                'count' => 0,
                'message' => 'Error scanning table: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get a specific item by primary key
     */
    public function getItem($tableName, $key)
    {
        try {
            $params = [
                'TableName' => $tableName,
                'Key' => $key
            ];

            $result = $this->dynamoDb->getItem($params);

            return [
                'success' => true,
                'data' => $result['Item'] ?? null,
                'message' => 'Item retrieved successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Get Item Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error getting item: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Query items with conditions
     */
    public function queryItems($tableName, $keyConditionExpression, $expressionAttributeValues = [], $limit = 100)
    {
        try {
            $params = [
                'TableName' => $tableName,
                'KeyConditionExpression' => $keyConditionExpression,
                'ExpressionAttributeValues' => $expressionAttributeValues,
                'Limit' => $limit
            ];

            $result = $this->dynamoDb->query($params);

            return [
                'success' => true,
                'data' => $result['Items'],
                'count' => $result['Count'],
                'scanned_count' => $result['ScannedCount'],
                'message' => 'Query executed successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Query Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => [],
                'message' => 'Error querying items: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Put (insert/update) an item
     */
    public function putItem($tableName, $item)
    {
        try {
            $params = [
                'TableName' => $tableName,
                'Item' => $item
            ];

            $result = $this->dynamoDb->putItem($params);

            return [
                'success' => true,
                'data' => $result,
                'message' => 'Item saved successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Put Item Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error saving item: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete an item
     */
    public function deleteItem($tableName, $key)
    {
        try {
            $params = [
                'TableName' => $tableName,
                'Key' => $key
            ];

            $result = $this->dynamoDb->deleteItem($params);

            return [
                'success' => true,
                'data' => $result,
                'message' => 'Item deleted successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Delete Item Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error deleting item: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create a new table
     */
    public function createTable($tableName, $keySchema, $attributeDefinitions, $billingMode = 'PAY_PER_REQUEST')
    {
        try {
            $params = [
                'TableName' => $tableName,
                'KeySchema' => $keySchema,
                'AttributeDefinitions' => $attributeDefinitions,
                'BillingMode' => $billingMode
            ];

            $result = $this->dynamoDb->createTable($params);

            return [
                'success' => true,
                'data' => $result,
                'message' => 'Table created successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Create Table Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error creating table: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Describe table structure
     */
    public function describeTable($tableName)
    {
        try {
            $result = $this->dynamoDb->describeTable([
                'TableName' => $tableName
            ]);

            return [
                'success' => true,
                'data' => $result['Table'],
                'message' => 'Table description retrieved successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Describe Table Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error describing table: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Batch get multiple items
     */
    public function batchGetItems($requestItems)
    {
        try {
            $params = [
                'RequestItems' => $requestItems
            ];

            $result = $this->dynamoDb->batchGetItem($params);

            return [
                'success' => true,
                'data' => $result['Responses'],
                'unprocessed_keys' => $result['UnprocessedKeys'] ?? [],
                'message' => 'Batch get completed successfully'
            ];
        } catch (DynamoDbException $e) {
            Log::error('DynamoDB Batch Get Error: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => [],
                'message' => 'Error in batch get: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Unmarshall DynamoDB item to regular array
     */
    private function unmarshallItem($item)
    {
        $unmarshalled = [];
        foreach ($item as $key => $value) {
            $unmarshalled[$key] = $this->unmarshallValue($value);
        }
        return $unmarshalled;
    }

    /**
     * Unmarshall DynamoDB value
     */
    private function unmarshallValue($value)
    {
        if (isset($value['S'])) {
            return $value['S'];
        } elseif (isset($value['N'])) {
            return is_numeric($value['N']) ? (float)$value['N'] : $value['N'];
        } elseif (isset($value['B'])) {
            return $value['B'];
        } elseif (isset($value['BOOL'])) {
            return $value['BOOL'];
        } elseif (isset($value['NULL'])) {
            return null;
        } elseif (isset($value['L'])) {
            $list = [];
            foreach ($value['L'] as $item) {
                $list[] = $this->unmarshallValue($item);
            }
            return $list;
        } elseif (isset($value['M'])) {
            $map = [];
            foreach ($value['M'] as $k => $v) {
                $map[$k] = $this->unmarshallValue($v);
            }
            return $map;
        } elseif (isset($value['SS'])) {
            return $value['SS'];
        } elseif (isset($value['NS'])) {
            return array_map('floatval', $value['NS']);
        } elseif (isset($value['BS'])) {
            return $value['BS'];
        }

        return $value;
    }
}
