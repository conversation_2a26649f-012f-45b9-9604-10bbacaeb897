@extends('backend.layouts.master')

@section('title','Delegates - DynamoDB')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Delegates ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($delegates) > 0)
                <table class="table table-bordered" id="delegate-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($delegates as $index => $delegate)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($delegate['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $delegate['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <strong>{{ $delegate['name'] ?? 'N/A' }}</strong>
                            </td>
                            <td>
                                @if(isset($delegate['email']))
                                    <a href="mailto:{{ $delegate['email'] }}">{{ $delegate['email'] }}</a>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($delegate['phone']))
                                    <a href="tel:{{ $delegate['phone'] }}">{{ $delegate['phone'] }}</a>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($delegate['status']))
                                    @switch($delegate['status'])
                                        @case('ACTIVE')
                                            <span class="badge badge-success">{{ $delegate['status'] }}</span>
                                            @break
                                        @case('INACTIVE')
                                            <span class="badge badge-danger">{{ $delegate['status'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $delegate['status'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($delegate['createdAt'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No delegates found!</h6>
                    <p>There are no delegates in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#delegate-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 7, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });
    });
</script>
@endpush
