@extends('backend.layouts.master')

@section('title','Delegates - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Delegates ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($delegates) > 0)
                <table class="table table-bordered" id="delegate-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Delegate Name</th>
                            <th>Profile Picture</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($delegates as $index => $delegate)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($delegate['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $delegate['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <strong>{{ $delegate['delegateName'] ?? 'N/A' }}</strong>
                            </td>
                            <td>
                                @if(isset($delegate['delegateProfilePicturePath']) && !empty($delegate['delegateProfilePicturePath']))
                                    <span class="badge badge-success">Available</span>
                                @else
                                    <span class="badge badge-secondary">Empty</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($delegate['createdAt'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No delegates found!</h6>
                    <p>There are no delegates in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delegate Details Modal -->
<div class="modal fade" id="delegateModal" tabindex="-1" role="dialog" aria-labelledby="delegateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="delegateModalLabel">Delegate Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="delegateDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#delegate-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 5, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full delegate details
        $('tbody').on('click', '.clickable-row', function() {
            var data = $('#delegate-dataTable').DataTable().row(this).data();
            if (data) {
                showDelegateDetails(this);
            }
        });
    });

    function showDelegateDetails(row) {
        // Get the delegate data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var delegateName = $(cells[3]).text();
        var profilePicture = $(cells[4]).find('.badge').text();
        var createdAt = $(cells[5]).text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Delegate Name:</th>
                            <td><strong>${delegateName}</strong></td>
                        </tr>
                        <tr>
                            <th>Profile Picture:</th>
                            <td>${profilePicture}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${createdAt}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#delegateDetails').html(detailsHtml);
        $('#delegateModal').modal('show');
    }
</script>
@endpush
