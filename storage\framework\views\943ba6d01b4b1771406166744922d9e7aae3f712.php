<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>CertHive-Admin-Portal || DASHBOARD</title>

    <!-- Custom fonts for this template-->
    <link href="<?php echo e(asset('backend/vendor/fontawesome-free/css/all.min.css')); ?>" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="<?php echo e(asset('backend/css/sb-admin-2.min.css')); ?>" rel="stylesheet">

    <!-- Custom Sidebar Scroll Styles -->
    <style>
        /* Make sidebar scrollable independently */
        .sidebar {
            position: fixed !important;
            top: 0;
            left: 0;
            height: 100vh !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            z-index: 1000;
            width: 224px; /* Default sidebar width */
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Adjust content wrapper to account for fixed sidebar */
        #content-wrapper {
            margin-left: 224px !important;
            width: calc(100% - 224px) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                margin-left: -224px;
            }

            #content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }

            .sidebar.toggled {
                width: 224px;
                margin-left: 0;
            }
        }

        /* Ensure sidebar brand stays at top */
        .sidebar-brand {
            position: sticky;
            top: 0;
            z-index: 1001;
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
        }

        /* Add some padding to the bottom of sidebar */
        .sidebar {
            padding-bottom: 2rem;
        }

        /* Smooth scrolling */
        .sidebar {
            scroll-behavior: smooth;
        }

        /* Improved sidebar alignment and spacing */
        .sidebar .nav-item {
            margin-bottom: 2px;
        }

        .sidebar .nav-link {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        .sidebar .sidebar-heading {
            font-size: 0.65rem;
            font-weight: 800;
            padding: 1.5rem 1rem 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05rem;
            color: rgba(255, 255, 255, 0.4);
        }

        .sidebar .collapse-item {
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            display: flex;
            align-items: center;
            font-size: 0.85rem;
        }

        .sidebar .collapse-item i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        /* Hover effects */
        .sidebar .nav-link:hover,
        .sidebar .collapse-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s;
        }

        /* Active state styling */
        .sidebar .nav-item.active .nav-link {
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #fff;
        }

        /* Divider styling */
        .sidebar .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15);
            margin: 1rem 0;
        }

        /* Brand styling */
        .sidebar .sidebar-brand {
            padding: 1.5rem 1rem;
            text-decoration: none;
        }

        .sidebar .sidebar-brand:hover {
            text-decoration: none;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>

</head><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/layouts/head.blade.php ENDPATH**/ ?>