<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('environments', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->json('config')->nullable(); // For storing environment-specific configurations
            $table->timestamps();
        });

        // Insert default environment records
        DB::table('environments')->insert([
            [
                'name' => 'Production',
                'slug' => 'production',
                'description' => 'Production environment for live application',
                'is_active' => true,
                'is_default' => false,
                'config' => json_encode([
                    'debug' => false,
                    'log_level' => 'error',
                    'cache_enabled' => true
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Development',
                'slug' => 'development',
                'description' => 'Development environment for testing and development',
                'is_active' => true,
                'is_default' => true,
                'config' => json_encode([
                    'debug' => true,
                    'log_level' => 'debug',
                    'cache_enabled' => false
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('environments');
    }
};
