<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes(['register'=>false]);

Route::get('/', function () {
    return view('frontend.welcome');
})->name('home');

// Backend section start

Route::group(['prefix'=>'/admin','middleware'=>['auth','admin']],function(){
    // Dashboard
    Route::get('/','AdminController@index')->name('admin');
    // Plans
    Route::resource('/plans','PlanController')->names('plans');
    // Countries
    Route::resource('/countries','CountryController')->names('countries');
    // States
    Route::resource('/states','StateController')->names('states');
    // Subscribers
    Route::resource('/subscribers','SubscriberController')->names('subscribers');
    // AJAX route for getting states by country
    Route::get('/get-states-by-country/{countryId}','SubscriberController@getStatesByCountry')
    ->name('get-states-by-country');
    // user route
    Route::resource('users','UsersController');
    // Profile
    Route::get('/profile','AdminController@profile')->name('admin-profile');
    Route::post('/profile/{id}','AdminController@profileUpdate')->name('profile-update');

    // Message
    Route::resource('/message','MessageController');
    Route::get('/message/five','MessageController@messageFive')->name('messages.five');
    // Notification
    Route::get('/notification/{id}','NotificationController@show')->name('admin.notification');
    Route::get('/notifications','NotificationController@index')->name('all.notification');
    Route::delete('/notification/{id}','NotificationController@delete')->name('notification.delete');
    // Password Change
    Route::get('change-password', 'AdminController@changePassword')->name('change.password.form');
    Route::post('change-password', 'AdminController@changPasswordStore')->name('change.password');

    // DynamoDB Management Routes
    Route::prefix('dynamodb')->name('dynamodb.')->group(function () {
        Route::get('/', 'DynamoDBManagementController@dashboard')->name('dashboard');
        Route::get('/table/{tableName}', 'DynamoDBManagementController@showTable')->name('table.show');
        Route::get('/app-notifications', 'DynamoDBManagementController@appNotifications')->name('app-notifications');
        Route::get('/certificates', 'DynamoDBManagementController@certificates')->name('certificates');
        Route::get('/certificate-recipients', 'DynamoDBManagementController@certificateRecipients')->name('certificate-recipients');
        Route::get('/delegates', 'DynamoDBManagementController@delegates')->name('delegates');
        Route::get('/recipients', 'DynamoDBManagementController@recipients')->name('recipients');
        Route::get('/subscriptions', 'DynamoDBManagementController@subscriptions')->name('subscriptions');
        Route::get('/users', 'DynamoDBManagementController@users')->name('users');
        Route::get('/item/{tableName}', 'DynamoDBManagementController@getItem')->name('item.get');
    });

});