<?php $__env->startSection('title','Recipients - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Recipients (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($recipients) > 0): ?>
                <table class="table table-bordered" id="recipient-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $recipients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $recipient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <code style="font-size: 10px;"><?php echo e(substr($recipient['id'] ?? 'N/A', 0, 20)); ?>...</code>
                            </td>
                            <td>
                                <span class="badge badge-info"><?php echo e($recipient['__typename'] ?? 'N/A'); ?></span>
                            </td>
                            <td>
                                <strong><?php echo e($recipient['name'] ?? 'N/A'); ?></strong>
                            </td>
                            <td>
                                <?php if(isset($recipient['email'])): ?>
                                    <a href="mailto:<?php echo e($recipient['email']); ?>"><?php echo e($recipient['email']); ?></a>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($recipient['phone'])): ?>
                                    <a href="tel:<?php echo e($recipient['phone']); ?>"><?php echo e($recipient['phone']); ?></a>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($recipient['status'])): ?>
                                    <?php switch($recipient['status']):
                                        case ('ACTIVE'): ?>
                                            <span class="badge badge-success"><?php echo e($recipient['status']); ?></span>
                                            <?php break; ?>
                                        <?php case ('INACTIVE'): ?>
                                            <span class="badge badge-danger"><?php echo e($recipient['status']); ?></span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($recipient['status']); ?></span>
                                    <?php endswitch; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($recipient['createdAt'] ?? null)); ?>

                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No recipients found!</h6>
                    <p>There are no recipients in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#recipient-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 7, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/recipients.blade.php ENDPATH**/ ?>