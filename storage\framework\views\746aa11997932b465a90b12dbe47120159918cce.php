
      <!-- Footer -->
      <footer class="sticky-footer bg-white">
        <div class="container my-auto">
          <div class="copyright text-center my-auto">
            <span>Copyright &copy; <a href="#" target="_blank">Xupoli</a> <?php echo e(date('Y')); ?></span>
          </div>
        </div>
      </footer>
      <!-- End of Footer -->

    </div>
    <!-- End of Content Wrapper -->

  </div>
  <!-- End of Page Wrapper -->

  <!-- Scroll to Top Button-->
  <a class="scroll-to-top rounded" href="#page-top">
    <i class="fas fa-angle-up"></i>
  </a>

  <!-- Logout Modal-->
  <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
          <button class="close" type="button" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
        <div class="modal-footer">
          <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
          <a class="btn btn-primary" href="login.html">Logout</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap core JavaScript-->
  <script src="<?php echo e(asset('backend/vendor/jquery/jquery.min.js')); ?>"></script>
  <script src="<?php echo e(asset('backend/vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>

  <!-- Core plugin JavaScript-->
  <script src="<?php echo e(asset('backend/vendor/jquery-easing/jquery.easing.min.js')); ?>"></script>

  <!-- Custom scripts for all pages-->
  <script src="<?php echo e(asset('backend/js/sb-admin-2.min.js')); ?>"></script>

  <!-- Page level plugins -->
  <script src="<?php echo e(asset('backend/vendor/chart.js/Chart.min.js')); ?>"></script>

  <!-- Page level custom scripts -->
  
  

  <?php echo $__env->yieldPushContent('scripts'); ?>

  <script>
    setTimeout(function(){
      $('.alert').slideUp();
    },4000);

    // Mode Toggle Functionality
    $(document).ready(function() {
        // Load current mode on page load
        loadCurrentMode();

        // Handle mode toggle
        $('#modeToggle').change(function() {
            toggleMode();
        });
    });

    function loadCurrentMode() {
        $.ajax({
            url: '<?php echo e(route("settings.current-mode")); ?>',
            type: 'GET',
            success: function(response) {
                updateModeDisplay(response.current_mode, response.is_live);
            },
            error: function() {
                $('#modeLabel').text('Error');
                $('#modeDescription').text('Failed to load mode');
            }
        });
    }

    function toggleMode() {
        // Show loading state
        $('#modeLabel').text('Switching...');
        $('#modeDescription').text('Please wait...');
        $('#modeToggle').prop('disabled', true);

        $.ajax({
            url: '<?php echo e(route("settings.toggle-mode")); ?>',
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    updateModeDisplay(response.current_mode, response.is_live);

                    // Show success message
                    showAlert('success', response.message);

                    // Optionally reload page after a delay to reflect changes
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('error', response.message);
                    // Revert toggle state
                    $('#modeToggle').prop('checked', !$('#modeToggle').prop('checked'));
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to toggle mode';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showAlert('error', errorMessage);
                // Revert toggle state
                $('#modeToggle').prop('checked', !$('#modeToggle').prop('checked'));
            },
            complete: function() {
                $('#modeToggle').prop('disabled', false);
            }
        });
    }

    function updateModeDisplay(mode, isLive) {
        $('#modeToggle').prop('checked', isLive);

        if (isLive) {
            $('#modeLabel').text('LIVE').removeClass('text-warning').addClass('text-success');
            $('#modeDescription').text('Production mode active');
            // Update header indicator
            $('#headerModeIndicator').removeClass('badge-warning badge-secondary')
                .addClass('badge-danger')
                .html('<i class="fas fa-circle fa-xs"></i> LIVE MODE');
        } else {
            $('#modeLabel').text('TEST').removeClass('text-success').addClass('text-warning');
            $('#modeDescription').text('Test mode active');
            // Update header indicator
            $('#headerModeIndicator').removeClass('badge-danger badge-secondary')
                .addClass('badge-warning')
                .html('<i class="fas fa-circle fa-xs"></i> TEST MODE');
        }
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <strong>${type === 'success' ? 'Success!' : 'Error!'}</strong> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        $('body').append(alertHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
  </script>
<?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/layouts/footer.blade.php ENDPATH**/ ?>