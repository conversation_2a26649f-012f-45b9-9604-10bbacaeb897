
      <!-- Footer -->
      <footer class="sticky-footer bg-white">
        <div class="container my-auto">
          <div class="copyright text-center my-auto">
            <span>Copyright &copy; <a href="#" target="_blank">Xupoli</a> <?php echo e(date('Y')); ?></span>
          </div>
        </div>
      </footer>
      <!-- End of Footer -->

    </div>
    <!-- End of Content Wrapper -->

  </div>
  <!-- End of Page Wrapper -->

  <!-- Scroll to Top Button-->
  <a class="scroll-to-top rounded" href="#page-top">
    <i class="fas fa-angle-up"></i>
  </a>

  <!-- Logout Modal-->
  <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
          <button class="close" type="button" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
        <div class="modal-footer">
          <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
          <a class="btn btn-primary" href="login.html">Logout</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap core JavaScript-->
  <script src="<?php echo e(asset('backend/vendor/jquery/jquery.min.js')); ?>"></script>
  <script src="<?php echo e(asset('backend/vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>

  <!-- Core plugin JavaScript-->
  <script src="<?php echo e(asset('backend/vendor/jquery-easing/jquery.easing.min.js')); ?>"></script>

  <!-- Custom scripts for all pages-->
  <script src="<?php echo e(asset('backend/js/sb-admin-2.min.js')); ?>"></script>

  <!-- Page level plugins -->
  <script src="<?php echo e(asset('backend/vendor/chart.js/Chart.min.js')); ?>"></script>

  <!-- Page level custom scripts -->
  
  

  <?php echo $__env->yieldPushContent('scripts'); ?>

  <script>
    setTimeout(function(){
      $('.alert').slideUp();
    },4000);

    // Enhanced sidebar scrolling functionality
    $(document).ready(function() {
        // Smooth scrolling for sidebar navigation
        $('.sidebar .nav-link').on('click', function(e) {
            // If it's a collapsible item, don't prevent default
            if ($(this).attr('data-toggle') === 'collapse') {
                return;
            }

            // Add active class management
            $('.sidebar .nav-item').removeClass('active');
            $(this).closest('.nav-item').addClass('active');
        });

        // Remember scroll position
        var sidebarScrollPosition = sessionStorage.getItem('sidebarScrollPosition');
        if (sidebarScrollPosition) {
            $('.sidebar').scrollTop(sidebarScrollPosition);
        }

        // Save scroll position when scrolling
        $('.sidebar').on('scroll', function() {
            sessionStorage.setItem('sidebarScrollPosition', $(this).scrollTop());
        });

        // Auto-expand DynamoDB section if on a DynamoDB page
        var currentPath = window.location.pathname;
        if (currentPath.includes('/dynamodb')) {
            $('#dynamodbCollapse').addClass('show');
            $('.sidebar .nav-item').removeClass('active');
            $('a[href*="dynamodb"]').closest('.nav-item').addClass('active');
        }

        // Highlight active menu item based on current URL
        $('.sidebar .nav-link, .sidebar .collapse-item').each(function() {
            var href = $(this).attr('href');
            if (href && currentPath.includes(href.split('/').pop())) {
                $(this).closest('.nav-item').addClass('active');
                // If it's inside a collapse, expand it
                var collapse = $(this).closest('.collapse');
                if (collapse.length) {
                    collapse.addClass('show');
                }
            }
        });
    });
  </script>
<?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/layouts/footer.blade.php ENDPATH**/ ?>