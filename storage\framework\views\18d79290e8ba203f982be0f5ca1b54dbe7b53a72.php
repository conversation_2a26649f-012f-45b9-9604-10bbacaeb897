<?php $__env->startSection('title','Create Country || Admin Panel'); ?>

<?php $__env->startSection('main-content'); ?>

<div class="card">
    <h5 class="card-header">Add Country</h5>
    <div class="card-body">
      <form method="post" action="<?php echo e(route('countries.store')); ?>" enctype="multipart/form-data">
        <?php echo e(csrf_field()); ?>

        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label for="code" class="col-form-label">Country Code <span class="text-danger">*</span></label>
                  <input id="code" type="text" name="code" placeholder="Enter country code (e.g., US, IN)" value="<?php echo e(old('code')); ?>" class="form-control" maxlength="3">
                  <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                  <label for="name" class="col-form-label">Country Name <span class="text-danger">*</span></label>
                  <input id="name" type="text" name="name" placeholder="Enter country name" value="<?php echo e(old('name')); ?>" class="form-control">
                  <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label for="serial_id" class="col-form-label">Serial ID</label>
                  <input id="serial_id" type="number" name="serial_id" placeholder="Enter serial ID" value="<?php echo e(old('serial_id')); ?>" class="form-control">
                  <?php $__errorArgs = ['serial_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                  <label for="phonecode" class="col-form-label">Phone Code</label>
                  <input id="phonecode" type="text" name="phonecode" placeholder="Enter phone code (e.g., 91, 1)" value="<?php echo e(old('phonecode')); ?>" class="form-control">
                  <?php $__errorArgs = ['phonecode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label for="timezone_id" class="col-form-label">Timezone</label>
                  <input id="timezone_id" type="text" name="timezone_id" placeholder="Enter timezone (e.g., Asia/Kolkata)" value="<?php echo e(old('timezone_id')); ?>" class="form-control">
                  <?php $__errorArgs = ['timezone_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                  <label for="currency_name" class="col-form-label">Currency Name</label>
                  <input id="currency_name" type="text" name="currency_name" placeholder="Enter currency name (e.g., Indian Rupee)" value="<?php echo e(old('currency_name')); ?>" class="form-control">
                  <?php $__errorArgs = ['currency_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label for="currency_code" class="col-form-label">Currency Code</label>
                  <input id="currency_code" type="text" name="currency_code" placeholder="Enter currency code (e.g., INR, USD)" value="<?php echo e(old('currency_code')); ?>" class="form-control" maxlength="3">
                  <?php $__errorArgs = ['currency_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                  <label for="currency_symbol" class="col-form-label">Currency Symbol</label>
                  <input id="currency_symbol" type="text" name="currency_symbol" placeholder="Enter currency symbol (e.g., ₹, $)" value="<?php echo e(old('currency_symbol')); ?>" class="form-control">
                  <?php $__errorArgs = ['currency_symbol'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label for="image" class="col-form-label">Country Flag</label>
                  <input id="image" type="file" name="image" class="form-control" accept="image/*">
                  <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                  <label for="is_active" class="col-form-label">Status <span class="text-danger">*</span></label>
                  <select name="is_active" class="form-control">
                      <option value="1" <?php echo e(old('is_active')=='1' ? 'selected' : ''); ?>>Active</option>
                      <option value="0" <?php echo e(old('is_active')=='0' ? 'selected' : ''); ?>>Inactive</option>
                  </select>
                  <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <span class="text-danger"><?php echo e($message); ?></span>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <div class="form-group mb-3">
           <button class="btn btn-success" type="submit">Submit</button>
           <a href="<?php echo e(route('countries.index')); ?>" class="btn btn-secondary">Cancel</a>
        </div>
      </form>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/countries/create.blade.php ENDPATH**/ ?>