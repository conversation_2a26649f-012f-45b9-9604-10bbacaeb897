@extends('backend.layouts.master')

@section('title','Recipients - DynamoDB')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Recipients ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($recipients) > 0)
                <table class="table table-bordered" id="recipient-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Recipient Info</th>
                            <th>Contact Details</th>
                            <th>Email Status</th>
                            <th>Fax Status</th>
                            <th>Preferred Mode</th>
                            <th>Country</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recipients as $index => $recipient)
                        <tr class="clickable-row" data-recipient-id="{{ $recipient['id'] ?? '' }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <div class="recipient-info">
                                    <strong>{{ $recipient['name'] ?? 'N/A' }}</strong>
                                    @if(isset($recipient['owner']))
                                        <br><small class="text-muted">Owner: {{ $recipient['owner'] }}</small>
                                    @endif
                                    @if(isset($recipient['userId']))
                                        <br><small class="text-muted">User: <code>{{ substr($recipient['userId'], 0, 15) }}...</code></small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="contact-details">
                                    @if(isset($recipient['email']))
                                        <div><i class="fas fa-envelope"></i> <a href="mailto:{{ $recipient['email'] }}">{{ $recipient['email'] }}</a></div>
                                    @endif
                                    @if(isset($recipient['fax']))
                                        <div><i class="fas fa-fax"></i>
                                            @if(isset($recipient['faxCode']))
                                                +{{ $recipient['faxCode'] }}
                                            @endif
                                            {{ $recipient['fax'] }}
                                        </div>
                                    @endif
                                    @if(!isset($recipient['email']) && !isset($recipient['fax']))
                                        <span class="text-muted">No contact info</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if(isset($recipient['emailStatus']))
                                    @switch($recipient['emailStatus'])
                                        @case('VERIFIED')
                                            <span class="badge badge-success"><i class="fas fa-check"></i> {{ $recipient['emailStatus'] }}</span>
                                            @break
                                        @case('UNVERIFIED')
                                            <span class="badge badge-warning"><i class="fas fa-exclamation"></i> {{ $recipient['emailStatus'] }}</span>
                                            @break
                                        @case('BOUNCED')
                                            <span class="badge badge-danger"><i class="fas fa-times"></i> {{ $recipient['emailStatus'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-info">{{ $recipient['emailStatus'] }}</span>
                                    @endswitch
                                    @if(isset($recipient['emailStatusReason']))
                                        <br><small class="text-muted">{{ substr($recipient['emailStatusReason'], 0, 30) }}{{ strlen($recipient['emailStatusReason']) > 30 ? '...' : '' }}</small>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['faxStatus']))
                                    @switch($recipient['faxStatus'])
                                        @case('VERIFIED')
                                            <span class="badge badge-success"><i class="fas fa-check"></i> {{ $recipient['faxStatus'] }}</span>
                                            @break
                                        @case('UNVERIFIED')
                                            <span class="badge badge-warning"><i class="fas fa-exclamation"></i> {{ $recipient['faxStatus'] }}</span>
                                            @break
                                        @case('FAILED')
                                            <span class="badge badge-danger"><i class="fas fa-times"></i> {{ $recipient['faxStatus'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-info">{{ $recipient['faxStatus'] }}</span>
                                    @endswitch
                                    @if(isset($recipient['faxStatusReason']))
                                        <br><small class="text-muted">{{ substr($recipient['faxStatusReason'], 0, 30) }}{{ strlen($recipient['faxStatusReason']) > 30 ? '...' : '' }}</small>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['preferredMode']))
                                    @switch($recipient['preferredMode'])
                                        @case('EMAIL')
                                            <span class="badge badge-primary"><i class="fas fa-envelope"></i> {{ $recipient['preferredMode'] }}</span>
                                            @break
                                        @case('FAX')
                                            <span class="badge badge-info"><i class="fas fa-fax"></i> {{ $recipient['preferredMode'] }}</span>
                                            @break
                                        @case('SMS')
                                            <span class="badge badge-warning"><i class="fas fa-sms"></i> {{ $recipient['preferredMode'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $recipient['preferredMode'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $recipient['countryName'] ?? 'N/A' }}</strong>
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-recipient='@json($recipient)'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if(isset($recipient['tempUrl']) && $recipient['tempUrl'])
                                    <a href="{{ $recipient['tempUrl'] }}" target="_blank" class="btn btn-primary btn-sm" data-toggle="tooltip" title="Open Temp URL">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                @endif
                                @if(isset($recipient['uploadedProfilePicturePath']) && $recipient['uploadedProfilePicturePath'])
                                    <button class="btn btn-success btn-sm view-picture" data-toggle="tooltip" title="View Profile Picture" data-picture="{{ $recipient['uploadedProfilePicturePath'] }}">
                                        <i class="fas fa-user-circle"></i>
                                    </button>
                                @endif
                                @if(isset($recipient['email']))
                                    <a href="mailto:{{ $recipient['email'] }}" class="btn btn-warning btn-sm" data-toggle="tooltip" title="Send Email">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No recipients found!</h6>
                    <p>There are no recipients in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Recipient Details Modal -->
<div class="modal fade" id="recipientModal" tabindex="-1" role="dialog" aria-labelledby="recipientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recipientModalLabel">Recipient Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="recipientDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Picture Viewer Modal -->
<div class="modal fade" id="pictureModal" tabindex="-1" role="dialog" aria-labelledby="pictureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pictureModalLabel">Profile Picture</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div id="pictureContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details, .view-picture {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
        margin: 2px;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
    .recipient-info, .contact-details {
        min-height: 40px;
    }
    .profile-picture-preview {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .picture-error {
        color: #dc3545;
        font-style: italic;
    }
    .status-reason {
        font-size: 0.8em;
        color: #6c757d;
        font-style: italic;
    }
    .contact-details div {
        margin-bottom: 3px;
    }
    .contact-details i {
        width: 16px;
        margin-right: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#recipient-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 0, "asc" ]], // Order by S.N.
            "columnDefs": [
                {
                    "targets": [7], // Actions column
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var recipient = $(this).data('recipient');
            showRecipientDetails(recipient);
        });

        // Handle view picture button click
        $('.view-picture').on('click', function() {
            var picture = $(this).data('picture');
            showPicture(picture, 'Profile Picture');
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showRecipientDetails(recipient) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${recipient.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${recipient.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td><strong>${recipient.name || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${recipient.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>User ID:</th>
                            <td><code>${recipient.userId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Country Name:</th>
                            <td><strong>${recipient.countryName || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>${recipient.email ? `<a href="mailto:${recipient.email}">${recipient.email}</a>` : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Email Status:</th>
                            <td>${getEmailStatusBadge(recipient.emailStatus)}</td>
                        </tr>
                        <tr>
                            <th>Email Status Reason:</th>
                            <td>${recipient.emailStatusReason || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Fax:</th>
                            <td>${recipient.fax ? (recipient.faxCode ? `+${recipient.faxCode} ${recipient.fax}` : recipient.fax) : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Fax Code:</th>
                            <td>${recipient.faxCode || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Fax Status:</th>
                            <td>${getFaxStatusBadge(recipient.faxStatus)}</td>
                        </tr>
                        <tr>
                            <th>Fax Status Reason:</th>
                            <td>${recipient.faxStatusReason || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Preferred Mode:</th>
                            <td>${getPreferredModeBadge(recipient.preferredMode)}</td>
                        </tr>
                        <tr>
                            <th>Profile Text:</th>
                            <td>${recipient.profileText || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${recipient.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${recipient.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Profile Picture:</th>
                            <td>${recipient.uploadedProfilePicturePath ? `<a href="${recipient.uploadedProfilePicturePath}" target="_blank">View Picture</a>` : 'Not Available'}</td>
                        </tr>
                        <tr>
                            <th>Temp URL:</th>
                            <td>${recipient.tempUrl ? `<a href="${recipient.tempUrl}" target="_blank">${recipient.tempUrl}</a>` : 'Not Available'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#recipientDetails').html(detailsHtml);
        $('#recipientModal').modal('show');
    }

    function showPicture(picturePath, title) {
        $('#pictureModalLabel').text(title);

        var pictureHtml = `
            <div class="text-center">
                <img src="${picturePath}" alt="${title}" class="profile-picture-preview"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div class="picture-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> Unable to load image<br>
                    <small>Path: ${picturePath}</small>
                </div>
            </div>
        `;

        $('#pictureContent').html(pictureHtml);
        $('#pictureModal').modal('show');
    }

    function getEmailStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'VERIFIED':
                return '<span class="badge badge-success"><i class="fas fa-check"></i> ' + status + '</span>';
            case 'UNVERIFIED':
                return '<span class="badge badge-warning"><i class="fas fa-exclamation"></i> ' + status + '</span>';
            case 'BOUNCED':
                return '<span class="badge badge-danger"><i class="fas fa-times"></i> ' + status + '</span>';
            default:
                return '<span class="badge badge-info">' + status + '</span>';
        }
    }

    function getFaxStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'VERIFIED':
                return '<span class="badge badge-success"><i class="fas fa-check"></i> ' + status + '</span>';
            case 'UNVERIFIED':
                return '<span class="badge badge-warning"><i class="fas fa-exclamation"></i> ' + status + '</span>';
            case 'FAILED':
                return '<span class="badge badge-danger"><i class="fas fa-times"></i> ' + status + '</span>';
            default:
                return '<span class="badge badge-info">' + status + '</span>';
        }
    }

    function getPreferredModeBadge(mode) {
        if (!mode) return '<span class="badge badge-secondary">N/A</span>';

        switch(mode) {
            case 'EMAIL':
                return '<span class="badge badge-primary"><i class="fas fa-envelope"></i> ' + mode + '</span>';
            case 'FAX':
                return '<span class="badge badge-info"><i class="fas fa-fax"></i> ' + mode + '</span>';
            case 'SMS':
                return '<span class="badge badge-warning"><i class="fas fa-sms"></i> ' + mode + '</span>';
            default:
                return '<span class="badge badge-secondary">' + mode + '</span>';
        }
    }
</script>
@endpush
