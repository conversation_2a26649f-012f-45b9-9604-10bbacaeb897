@extends('backend.layouts.master')

@section('title','Recipients - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Recipients ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($recipients) > 0)
                <table class="table table-bordered" id="recipient-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Country</th>
                            <th>Email</th>
                            <th>Email Status</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recipients as $index => $recipient)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($recipient['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $recipient['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                @if(isset($recipient['countryName']))
                                    <span class="badge badge-primary">{{ $recipient['countryName'] }}</span>
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['email']))
                                    {{ $recipient['email'] }}
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['emailStatus']))
                                    @if($recipient['emailStatus'] == 'ACTIVE')
                                        <span class="badge badge-success">{{ $recipient['emailStatus'] }}</span>
                                    @else
                                        <span class="badge badge-warning">{{ $recipient['emailStatus'] }}</span>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['createdAt']))
                                    {{ \Carbon\Carbon::parse($recipient['createdAt'])->format('Y-m-d H:i:s') }}
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No recipients found!</h6>
                    <p>There are no recipients in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Recipient Details Modal -->
<div class="modal fade" id="recipientModal" tabindex="-1" role="dialog" aria-labelledby="recipientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recipientModalLabel">Recipient Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="recipientDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#recipient-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full recipient details
        $('tbody').on('click', '.clickable-row', function() {
            var data = $('#recipient-dataTable').DataTable().row(this).data();
            if (data) {
                showRecipientDetails(this);
            }
        });
    });

    function showRecipientDetails(row) {
        // Get the recipient data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var country = $(cells[3]).find('.badge').text();
        var email = $(cells[4]).text();
        var emailStatus = $(cells[5]).find('.badge').text();
        var createdAt = $(cells[6]).text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Country:</th>
                            <td><span class="badge badge-primary">${country}</span></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>${email}</td>
                        </tr>
                        <tr>
                            <th>Email Status:</th>
                            <td>${emailStatus}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${createdAt}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#recipientDetails').html(detailsHtml);
        $('#recipientModal').modal('show');
    }
</script>
@endpush
