@extends('backend.layouts.master')

@section('title','DynamoDB Users')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">DynamoDB Users ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($users) > 0)
                <table class="table table-bordered" id="user-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Access Level</th>
                            <th>Country</th>
                            <th>Device Tokens</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $index => $user)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($user['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $user['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                @if(isset($user['accessLevel']))
                                    @if($user['accessLevel'] == 'Public')
                                        <span class="badge badge-success">{{ $user['accessLevel'] }}</span>
                                    @else
                                        <span class="badge badge-warning">{{ $user['accessLevel'] }}</span>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($user['country']))
                                    <span class="badge badge-primary">{{ $user['country'] }}</span>
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($user['deviceTokens']) && is_array($user['deviceTokens']) && count($user['deviceTokens']) > 0)
                                    <span class="badge badge-info">{{ count($user['deviceTokens']) }} tokens</span>
                                @else
                                    <span class="badge badge-secondary">No tokens</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($user['createdAt'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No users found!</h6>
                    <p>There are no users in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">User Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="userDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#user-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1, 5], // ID and device tokens columns
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full user details
        $('tbody').on('click', '.clickable-row', function() {
            var data = $('#user-dataTable').DataTable().row(this).data();
            if (data) {
                showUserDetails(this);
            }
        });
    });

    function showUserDetails(row) {
        // Get the user data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var accessLevel = $(cells[3]).find('.badge').text();
        var country = $(cells[4]).find('.badge').text();
        var deviceTokens = $(cells[5]).find('.badge').text();
        var createdAt = $(cells[6]).text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Access Level:</th>
                            <td>${accessLevel}</td>
                        </tr>
                        <tr>
                            <th>Country:</th>
                            <td>${country}</td>
                        </tr>
                        <tr>
                            <th>Device Tokens:</th>
                            <td>${deviceTokens}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${createdAt}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#userDetails').html(detailsHtml);
        $('#userModal').modal('show');
    }
</script>
@endpush
