@extends('backend.layouts.master')

@section('title','DynamoDB Users')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">DynamoDB Users ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($users) > 0)
                <table class="table table-bordered" id="user-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>User Info</th>
                            <th>Contact Details</th>
                            <th>Access Level</th>
                            <th>Security</th>
                            <th>Location</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $index => $user)
                        <tr class="clickable-row" data-user-id="{{ $user['id'] ?? '' }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <div class="user-info">
                                    <strong>{{ $user['fullName'] ?? $user['userName'] ?? 'N/A' }}</strong>
                                    @if(isset($user['userName']) && $user['userName'] !== ($user['fullName'] ?? ''))
                                        <br><small class="text-muted">@{{ $user['userName'] }}</small>
                                    @endif
                                    @if(isset($user['owner']))
                                        <br><small class="text-muted">Owner: {{ $user['owner'] }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="contact-details">
                                    @if(isset($user['email']))
                                        <div><i class="fas fa-envelope"></i> <a href="mailto:{{ $user['email'] }}">{{ $user['email'] }}</a></div>
                                    @endif
                                    @if(isset($user['deviceTokens']) && is_array($user['deviceTokens']) && count($user['deviceTokens']) > 0)
                                        <div><i class="fas fa-mobile-alt"></i> {{ count($user['deviceTokens']) }} device(s)</div>
                                    @elseif(isset($user['deviceTokens']) && !is_array($user['deviceTokens']) && $user['deviceTokens'])
                                        <div><i class="fas fa-mobile-alt"></i> 1 device</div>
                                    @endif
                                    @if(!isset($user['email']) && (!isset($user['deviceTokens']) || empty($user['deviceTokens'])))
                                        <span class="text-muted">No contact info</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if(isset($user['accessLevel']))
                                    @switch($user['accessLevel'])
                                        @case('ADMIN')
                                            <span class="badge badge-danger"><i class="fas fa-crown"></i> {{ $user['accessLevel'] }}</span>
                                            @break
                                        @case('MODERATOR')
                                            <span class="badge badge-warning"><i class="fas fa-shield-alt"></i> {{ $user['accessLevel'] }}</span>
                                            @break
                                        @case('USER')
                                            <span class="badge badge-primary"><i class="fas fa-user"></i> {{ $user['accessLevel'] }}</span>
                                            @break
                                        @case('PREMIUM')
                                            <span class="badge badge-success"><i class="fas fa-star"></i> {{ $user['accessLevel'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $user['accessLevel'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                <div class="security-info">
                                    @if(isset($user['twoFactor']))
                                        @if($user['twoFactor'] === true || $user['twoFactor'] === 'true' || $user['twoFactor'] === 'enabled')
                                            <span class="badge badge-success"><i class="fas fa-lock"></i> 2FA Enabled</span>
                                        @else
                                            <span class="badge badge-warning"><i class="fas fa-unlock"></i> 2FA Disabled</span>
                                        @endif
                                    @else
                                        <span class="badge badge-secondary">2FA N/A</span>
                                    @endif
                                    @if(isset($user['settings']) && $user['settings'])
                                        <br><span class="badge badge-info"><i class="fas fa-cog"></i> Settings Available</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="location-info">
                                    @if(isset($user['country']))
                                        <strong>{{ $user['country'] }}</strong>
                                    @endif
                                    @if(isset($user['location']))
                                        <br><small class="text-muted">{{ $user['location'] }}</small>
                                    @endif
                                    @if(!isset($user['country']) && !isset($user['location']))
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($user['createdAt'] ?? null) }}
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-user='@json($user)'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if(isset($user['tempUrl']) && $user['tempUrl'])
                                    <a href="{{ $user['tempUrl'] }}" target="_blank" class="btn btn-primary btn-sm" data-toggle="tooltip" title="Open Temp URL">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                @endif
                                @if(isset($user['uploadedProfilePicturePath']) && $user['uploadedProfilePicturePath'])
                                    <button class="btn btn-success btn-sm view-picture" data-toggle="tooltip" title="View Profile Picture" data-picture="{{ $user['uploadedProfilePicturePath'] }}">
                                        <i class="fas fa-user-circle"></i>
                                    </button>
                                @endif
                                @if(isset($user['email']))
                                    <a href="mailto:{{ $user['email'] }}" class="btn btn-warning btn-sm" data-toggle="tooltip" title="Send Email">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                                @if(isset($user['settings']) && $user['settings'])
                                    <button class="btn btn-secondary btn-sm view-settings" data-toggle="tooltip" title="View Settings" data-settings="{{ $user['settings'] }}">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No users found!</h6>
                    <p>There are no users in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">User Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="userDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Picture Viewer Modal -->
<div class="modal fade" id="pictureModal" tabindex="-1" role="dialog" aria-labelledby="pictureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pictureModalLabel">Profile Picture</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div id="pictureContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" role="dialog" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">User Settings</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="settingsContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details, .view-picture, .view-settings {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
        margin: 2px;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
    .user-info, .contact-details, .security-info, .location-info {
        min-height: 40px;
    }
    .profile-picture-preview {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .picture-error {
        color: #dc3545;
        font-style: italic;
    }
    .settings-data {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        white-space: pre-wrap;
        word-break: break-all;
    }
    .contact-details div {
        margin-bottom: 3px;
    }
    .contact-details i {
        width: 16px;
        margin-right: 5px;
    }
    .device-tokens {
        background-color: #e9ecef;
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin-top: 0.5rem;
        font-family: 'Courier New', monospace;
        font-size: 0.8em;
        max-height: 200px;
        overflow-y: auto;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#user-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [7], // Actions column
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var user = $(this).data('user');
            showUserDetails(user);
        });

        // Handle view picture button click
        $('.view-picture').on('click', function() {
            var picture = $(this).data('picture');
            showPicture(picture, 'Profile Picture');
        });

        // Handle view settings button click
        $('.view-settings').on('click', function() {
            var settings = $(this).data('settings');
            showSettings(settings);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showUserDetails(user) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${user.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${user.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Full Name:</th>
                            <td><strong>${user.fullName || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>User Name:</th>
                            <td><strong>@${user.userName || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${user.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>${user.email ? `<a href="mailto:${user.email}">${user.email}</a>` : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Access Level:</th>
                            <td>${getAccessLevelBadge(user.accessLevel)}</td>
                        </tr>
                        <tr>
                            <th>Two Factor Authentication:</th>
                            <td>${getTwoFactorBadge(user.twoFactor)}</td>
                        </tr>
                        <tr>
                            <th>Country:</th>
                            <td><strong>${user.country || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Location:</th>
                            <td>${user.location || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Profile Text:</th>
                            <td>${user.profileText || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${user.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${user.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Profile Picture:</th>
                            <td>${user.uploadedProfilePicturePath ? `<a href="${user.uploadedProfilePicturePath}" target="_blank">View Picture</a>` : 'Not Available'}</td>
                        </tr>
                        <tr>
                            <th>Temp URL:</th>
                            <td>${user.tempUrl ? `<a href="${user.tempUrl}" target="_blank">${user.tempUrl}</a>` : 'Not Available'}</td>
                        </tr>
                        <tr>
                            <th>Settings:</th>
                            <td>${user.settings ? '<span class="badge badge-success">Available</span>' : '<span class="badge badge-secondary">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>Device Tokens:</th>
                            <td>${getDeviceTokensDisplay(user.deviceTokens)}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#userDetails').html(detailsHtml);
        $('#userModal').modal('show');
    }

    function showPicture(picturePath, title) {
        $('#pictureModalLabel').text(title);

        var pictureHtml = `
            <div class="text-center">
                <img src="${picturePath}" alt="${title}" class="profile-picture-preview"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div class="picture-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> Unable to load image<br>
                    <small>Path: ${picturePath}</small>
                </div>
            </div>
        `;

        $('#pictureContent').html(pictureHtml);
        $('#pictureModal').modal('show');
    }

    function showSettings(settingsData) {
        var settingsHtml = `
            <div class="settings-data">
                <h6><i class="fas fa-cog"></i> User Settings</h6>
                ${settingsData}
            </div>
        `;

        $('#settingsContent').html(settingsHtml);
        $('#settingsModal').modal('show');
    }

    function getAccessLevelBadge(accessLevel) {
        if (!accessLevel) return '<span class="badge badge-secondary">N/A</span>';

        switch(accessLevel) {
            case 'ADMIN':
                return '<span class="badge badge-danger"><i class="fas fa-crown"></i> ' + accessLevel + '</span>';
            case 'MODERATOR':
                return '<span class="badge badge-warning"><i class="fas fa-shield-alt"></i> ' + accessLevel + '</span>';
            case 'USER':
                return '<span class="badge badge-primary"><i class="fas fa-user"></i> ' + accessLevel + '</span>';
            case 'PREMIUM':
                return '<span class="badge badge-success"><i class="fas fa-star"></i> ' + accessLevel + '</span>';
            default:
                return '<span class="badge badge-secondary">' + accessLevel + '</span>';
        }
    }

    function getTwoFactorBadge(twoFactor) {
        if (twoFactor === true || twoFactor === 'true' || twoFactor === 'enabled') {
            return '<span class="badge badge-success"><i class="fas fa-lock"></i> Enabled</span>';
        } else if (twoFactor === false || twoFactor === 'false' || twoFactor === 'disabled') {
            return '<span class="badge badge-warning"><i class="fas fa-unlock"></i> Disabled</span>';
        } else {
            return '<span class="badge badge-secondary">N/A</span>';
        }
    }

    function getDeviceTokensDisplay(deviceTokens) {
        if (!deviceTokens) {
            return '<span class="text-muted">No devices</span>';
        }

        if (Array.isArray(deviceTokens)) {
            if (deviceTokens.length === 0) {
                return '<span class="text-muted">No devices</span>';
            }

            var tokensHtml = `<span class="badge badge-info">${deviceTokens.length} device(s)</span>`;
            tokensHtml += '<div class="device-tokens">';
            deviceTokens.forEach((token, index) => {
                tokensHtml += `<div><strong>Device ${index + 1}:</strong> ${token}</div>`;
            });
            tokensHtml += '</div>';
            return tokensHtml;
        } else {
            return `<span class="badge badge-info">1 device</span><div class="device-tokens"><div><strong>Device:</strong> ${deviceTokens}</div></div>`;
        }
    }
</script>
@endpush
