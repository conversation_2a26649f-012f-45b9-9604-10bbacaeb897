@extends('backend.layouts.master')

@section('title','Certificates - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Certificates ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($certificates) > 0)
                <table class="table table-bordered" id="certificate-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Certificate Name</th>
                            <th>Owner</th>
                            <th>Certificate Status</th>
                            <th>Archive Status</th>
                            <th>User Name</th>
                            <th>Effective Date</th>
                            <th>Expiration Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($certificates as $index => $certificate)
                        <tr class="clickable-row" data-certificate-id="{{ $certificate['id'] ?? '' }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <strong>{{ $certificate['name'] ?? 'N/A' }}</strong>
                                @if(isset($certificate['id']))
                                    <br><small class="text-muted"><code>{{ substr($certificate['id'], 0, 25) }}...</code></small>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $certificate['owner'] ?? 'N/A' }}</strong>
                                @if(isset($certificate['userId']))
                                    <br><small class="text-muted">ID: <code>{{ substr($certificate['userId'], 0, 15) }}...</code></small>
                                @endif
                            </td>
                            <td>
                                @if(isset($certificate['certificateStatus']))
                                    @switch($certificate['certificateStatus'])
                                        @case('ACTIVE')
                                            <span class="badge badge-success">{{ $certificate['certificateStatus'] }}</span>
                                            @break
                                        @case('INACTIVE')
                                            <span class="badge badge-danger">{{ $certificate['certificateStatus'] }}</span>
                                            @break
                                        @case('EXPIRED')
                                            <span class="badge badge-warning">{{ $certificate['certificateStatus'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $certificate['certificateStatus'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($certificate['archiveStatus']))
                                    @if($certificate['archiveStatus'] == 'NotArchived')
                                        <span class="badge badge-success">Not Archived</span>
                                    @elseif($certificate['archiveStatus'] == 'Archived')
                                        <span class="badge badge-info">Archived</span>
                                    @else
                                        <span class="badge badge-warning">{{ $certificate['archiveStatus'] }}</span>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $certificate['userName'] ?? 'N/A' }}</strong>
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($certificate['effectiveDate'] ?? null) }}
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($certificate['expirationDate'] ?? null) }}
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-certificate='@json($certificate)'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if(isset($certificate['uploadedCertificatePath']) && $certificate['uploadedCertificatePath'])
                                    <a href="{{ $certificate['uploadedCertificatePath'] }}" target="_blank" class="btn btn-primary btn-sm" data-toggle="tooltip" title="View Certificate">
                                        <i class="fas fa-certificate"></i>
                                    </a>
                                @endif
                                @if(isset($certificate['certFront']) && $certificate['certFront'])
                                    <button class="btn btn-success btn-sm" data-toggle="tooltip" title="Front Available">
                                        <i class="fas fa-image"></i>
                                    </button>
                                @endif
                                @if(isset($certificate['certBack']) && $certificate['certBack'])
                                    <button class="btn btn-warning btn-sm" data-toggle="tooltip" title="Back Available">
                                        <i class="fas fa-images"></i>
                                    </button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No certificates found!</h6>
                    <p>There are no certificates in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Certificate Details Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1" role="dialog" aria-labelledby="certificateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="certificateModalLabel">Certificate Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="certificateDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#certificate-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by effective date
            "columnDefs": [
                {
                    "targets": [8], // Actions column
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var certificate = $(this).data('certificate');
            showCertificateDetails(certificate);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showCertificateDetails(certificate) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${certificate.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${certificate.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Certificate Name:</th>
                            <td><strong>${certificate.name || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${certificate.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>User Name:</th>
                            <td><strong>${certificate.userName || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>User ID:</th>
                            <td><code>${certificate.userId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Certificate Status:</th>
                            <td>${getCertificateStatusBadge(certificate.certificateStatus)}</td>
                        </tr>
                        <tr>
                            <th>Archive Status:</th>
                            <td>${getArchiveStatusBadge(certificate.archiveStatus)}</td>
                        </tr>
                        <tr>
                            <th>Effective Date:</th>
                            <td>${certificate.effectiveDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Expiration Date:</th>
                            <td>${certificate.expirationDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${certificate.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${certificate.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Certificate Front:</th>
                            <td>${certificate.certFront ? '<span class="badge badge-success">Available</span>' : '<span class="badge badge-secondary">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>Certificate Back:</th>
                            <td>${certificate.certBack ? '<span class="badge badge-success">Available</span>' : '<span class="badge badge-secondary">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>Uploaded Certificate Path:</th>
                            <td>${certificate.uploadedCertificatePath ? `<a href="${certificate.uploadedCertificatePath}" target="_blank">${certificate.uploadedCertificatePath}</a>` : 'N/A'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#certificateDetails').html(detailsHtml);
        $('#certificateModal').modal('show');
    }

    function getCertificateStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'ACTIVE':
                return '<span class="badge badge-success">' + status + '</span>';
            case 'INACTIVE':
                return '<span class="badge badge-danger">' + status + '</span>';
            case 'EXPIRED':
                return '<span class="badge badge-warning">' + status + '</span>';
            default:
                return '<span class="badge badge-secondary">' + status + '</span>';
        }
    }

    function getArchiveStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'NotArchived':
                return '<span class="badge badge-success">Not Archived</span>';
            case 'Archived':
                return '<span class="badge badge-info">Archived</span>';
            default:
                return '<span class="badge badge-warning">' + status + '</span>';
        }
    }
</script>
@endpush
