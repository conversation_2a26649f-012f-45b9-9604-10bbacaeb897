
<?php $__env->startSection('title','CertHive-Admin-Portal || DASHBOARD'); ?>
<?php $__env->startSection('main-content'); ?>
<div class="container-fluid">
    <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
      <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
    </div>

    <!-- Content Row -->
    <div class="row">





      <!-- Plans Count -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Plans</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($planCount); ?></div>
              </div>
              <div class="col-auto">
                <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Countries Count -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Countries</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($countryCount); ?></div>
              </div>
              <div class="col-auto">
                <i class="fas fa-globe fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- States Count -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total States</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stateCount); ?></div>
              </div>
              <div class="col-auto">
                <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Admins -->
      <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Admins</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($totalAdminCount); ?></div>
              </div>
              <div class="col-auto">
                <i class="fas fa-users-cog fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Second Row - Subscriber Statistics -->
    <div class="row">
      <div class="col-xl-6 col-md-6 mb-4">
      <!-- Active Subscribers -->
      <div class="col-xl-12 col-md-12 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Subscribers</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($activeSubscriberCount); ?></div>
              </div>
              <div class="col-auto">
                <i class="fas fa-user-check fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Inactive Subscribers -->
      <div class="col-xl-12 col-md-12 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive Subscribers</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($inactiveSubscriberCount); ?></div>
                <small class="text-muted">Includes inactive, expired & suspended</small>
              </div>
              <div class="col-auto">
                <i class="fas fa-user-times fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      <div class="col-xl-6 col-md-6 mb-4">

            <div class="col-xl-12 col-lg-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Quick Statistics</h6>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <div class="small text-gray-500">Active Plans</div>
              <div class="h5 font-weight-bold"><?php echo e($activePlans); ?> / <?php echo e($totalPlans); ?></div>
            </div>
            <div class="mb-3">
              <div class="small text-gray-500">Total Subscribers</div>
              <div class="h5 font-weight-bold"><?php echo e($activeSubscriberCount + $inactiveSubscriberCount); ?></div>
            </div>
            <div class="mb-3">
              <div class="small text-gray-500">Subscriber Success Rate</div>
              <div class="h5 font-weight-bold">
                <?php if(($activeSubscriberCount + $inactiveSubscriberCount) > 0): ?>
                  <?php echo e(round(($activeSubscriberCount / ($activeSubscriberCount + $inactiveSubscriberCount)) * 100, 1)); ?>%
                <?php else: ?>
                  0%
                <?php endif; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

    </div>


    <!-- Subscription Charts Row -->
    <div class="row">
      <!-- Active Subscriptions by Country -->
      <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-success">Active Subscriptions by Country</h6>
          </div>
          <div class="card-body">
            <div id="active_country_chart" style="width: 100%; height: 400px;"></div>
          </div>
        </div>
      </div>

      <!-- Inactive Subscriptions by Country -->
      <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-danger">Inactive Subscriptions by Country</h6>
          </div>
          <div class="card-body">
            <div id="inactive_country_chart" style="width: 100%; height: 400px;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- State Charts Row -->
    <div class="row">
      <!-- Active Subscriptions by State -->
      <div class="col-xl-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-success">Active Subscriptions by State (Country-wise)</h6>
          </div>
          <div class="card-body">
            <div id="active_state_charts_container"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Inactive Subscriptions by State -->
      <div class="col-xl-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-danger">Inactive Subscriptions by State (Country-wise)</h6>
          </div>
          <div class="card-body">
            <div id="inactive_state_charts_container"></div>
          </div>
        </div>
      </div>
    </div>

  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<script type="text/javascript">
  // Chart data from controller
  var activeCountryData = <?php echo $activeCountryChartData; ?>;
  var inactiveCountryData = <?php echo $inactiveCountryChartData; ?>;
  var activeStateData = <?php echo $activeStateChartData; ?>;
  var inactiveStateData = <?php echo $inactiveStateChartData; ?>;

  google.charts.load('current', {'packages':['corechart', 'bar']});
  google.charts.setOnLoadCallback(drawAllCharts);

  function drawAllCharts() {
    drawActiveCountryChart();
    drawInactiveCountryChart();
    drawActiveStateCharts();
    drawInactiveStateCharts();
  }

  // Active Subscriptions by Country Chart
  function drawActiveCountryChart() {
    if (activeCountryData.length > 1) {
      var data = google.visualization.arrayToDataTable(activeCountryData);
      var options = {
        title: 'Active Subscriptions by Country',
        titleTextStyle: {
          color: '#28a745',
          fontSize: 16,
          bold: true
        },
        hAxis: {
          title: 'Number of Active Subscriptions',
          titleTextStyle: {color: '#333'},
          textStyle: {color: '#666'}
        },
        vAxis: {
          title: 'Countries',
          titleTextStyle: {color: '#333'},
          textStyle: {color: '#666'}
        },
        colors: ['#28a745'],
        backgroundColor: 'transparent',
        legend: {position: 'none'},
        chartArea: {left: 100, top: 50, width: '75%', height: '80%'}
      };
      var chart = new google.visualization.BarChart(document.getElementById('active_country_chart'));
      chart.draw(data, options);
    } else {
      document.getElementById('active_country_chart').innerHTML = '<div class="text-center text-muted p-4"><h5>No Active Subscriptions Data Available</h5></div>';
    }
  }

  // Inactive Subscriptions by Country Chart
  function drawInactiveCountryChart() {
    if (inactiveCountryData.length > 1) {
      var data = google.visualization.arrayToDataTable(inactiveCountryData);
      var options = {
        title: 'Inactive Subscriptions by Country',
        titleTextStyle: {
          color: '#dc3545',
          fontSize: 16,
          bold: true
        },
        hAxis: {
          title: 'Number of Inactive Subscriptions',
          titleTextStyle: {color: '#333'},
          textStyle: {color: '#666'}
        },
        vAxis: {
          title: 'Countries',
          titleTextStyle: {color: '#333'},
          textStyle: {color: '#666'}
        },
        colors: ['#dc3545'],
        backgroundColor: 'transparent',
        legend: {position: 'none'},
        chartArea: {left: 100, top: 50, width: '75%', height: '80%'}
      };
      var chart = new google.visualization.BarChart(document.getElementById('inactive_country_chart'));
      chart.draw(data, options);
    } else {
      document.getElementById('inactive_country_chart').innerHTML = '<div class="text-center text-muted p-4"><h5>No Inactive Subscriptions Data Available</h5></div>';
    }
  }

  // Active Subscriptions by State Charts (Multiple charts by country)
  function drawActiveStateCharts() {
    var container = document.getElementById('active_state_charts_container');
    container.innerHTML = '';

    if (Object.keys(activeStateData).length === 0) {
      container.innerHTML = '<div class="text-center text-muted p-4"><h5>No Active State Subscription Data Available</h5></div>';
      return;
    }

    Object.keys(activeStateData).forEach(function(country, index) {
      var chartDiv = document.createElement('div');
      chartDiv.id = 'active_state_chart_' + index;
      chartDiv.style.width = '100%';
      chartDiv.style.height = '400px';
      chartDiv.style.marginBottom = '20px';

      var titleDiv = document.createElement('h5');
      titleDiv.innerHTML = country + ' - Active Subscriptions by State';
      titleDiv.className = 'text-success mb-3';

      container.appendChild(titleDiv);
      container.appendChild(chartDiv);

      if (activeStateData[country].length > 1) {
        var data = google.visualization.arrayToDataTable(activeStateData[country]);
        var options = {
          title: country + ' - Active Subscriptions by State',
          titleTextStyle: {
            color: '#28a745',
            fontSize: 14,
            bold: true
          },
          hAxis: {
            title: 'Number of Active Subscriptions',
            titleTextStyle: {color: '#333'},
            textStyle: {color: '#666'}
          },
          vAxis: {
            title: 'States',
            titleTextStyle: {color: '#333'},
            textStyle: {color: '#666'}
          },
          colors: ['#28a745'],
          backgroundColor: 'transparent',
          legend: {position: 'none'},
          chartArea: {left: 100, top: 50, width: '75%', height: '80%'}
        };
        var chart = new google.visualization.BarChart(chartDiv);
        chart.draw(data, options);
      } else {
        chartDiv.innerHTML = '<div class="text-center text-muted p-4"><h6>No data available for ' + country + '</h6></div>';
      }
    });
  }

  // Inactive Subscriptions by State Charts (Multiple charts by country)
  function drawInactiveStateCharts() {
    var container = document.getElementById('inactive_state_charts_container');
    container.innerHTML = '';

    if (Object.keys(inactiveStateData).length === 0) {
      container.innerHTML = '<div class="text-center text-muted p-4"><h5>No Inactive State Subscription Data Available</h5></div>';
      return;
    }

    Object.keys(inactiveStateData).forEach(function(country, index) {
      var chartDiv = document.createElement('div');
      chartDiv.id = 'inactive_state_chart_' + index;
      chartDiv.style.width = '100%';
      chartDiv.style.height = '400px';
      chartDiv.style.marginBottom = '20px';

      var titleDiv = document.createElement('h5');
      titleDiv.innerHTML = country + ' - Inactive Subscriptions by State';
      titleDiv.className = 'text-danger mb-3';

      container.appendChild(titleDiv);
      container.appendChild(chartDiv);

      if (inactiveStateData[country].length > 1) {
        var data = google.visualization.arrayToDataTable(inactiveStateData[country]);
        var options = {
          title: country + ' - Inactive Subscriptions by State',
          titleTextStyle: {
            color: '#dc3545',
            fontSize: 14,
            bold: true
          },
          hAxis: {
            title: 'Number of Inactive Subscriptions',
            titleTextStyle: {color: '#333'},
            textStyle: {color: '#666'}
          },
          vAxis: {
            title: 'States',
            titleTextStyle: {color: '#333'},
            textStyle: {color: '#666'}
          },
          colors: ['#dc3545'],
          backgroundColor: 'transparent',
          legend: {position: 'none'},
          chartArea: {left: 100, top: 50, width: '75%', height: '80%'}
        };
        var chart = new google.visualization.BarChart(chartDiv);
        chart.draw(data, options);
      } else {
        chartDiv.innerHTML = '<div class="text-center text-muted p-4"><h6>No data available for ' + country + '</h6></div>';
      }
    });
  }

  // Responsive charts
  window.addEventListener('resize', function() {
    drawAllCharts();
  });
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/index.blade.php ENDPATH**/ ?>