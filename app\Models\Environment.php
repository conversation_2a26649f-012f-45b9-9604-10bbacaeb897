<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Environment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'is_default',
        'config'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'config' => 'array'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug from name
        static::creating(function ($environment) {
            if (empty($environment->slug)) {
                $environment->slug = Str::slug($environment->name);
            }
        });

        static::updating(function ($environment) {
            if ($environment->isDirty('name') && empty($environment->slug)) {
                $environment->slug = Str::slug($environment->name);
            }
        });

        // Ensure only one default environment
        static::saving(function ($environment) {
            if ($environment->is_default) {
                static::where('id', '!=', $environment->id)->update(['is_default' => false]);
            }
        });
    }

    /**
     * Scope for active environments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default environment
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the default environment
     */
    public static function getDefault()
    {
        return static::default()->first();
    }

    /**
     * Get environment by slug
     */
    public static function findBySlug($slug)
    {
        return static::where('slug', $slug)->first();
    }

    /**
     * Check if this is the production environment
     */
    public function isProduction()
    {
        return $this->slug === 'production';
    }

    /**
     * Check if this is the development environment
     */
    public function isDevelopment()
    {
        return $this->slug === 'development';
    }

    /**
     * Get configuration value
     */
    public function getConfig($key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * Set configuration value
     */
    public function setConfig($key, $value)
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        return $this;
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeAttribute()
    {
        if (!$this->is_active) {
            return 'badge-secondary';
        }
        
        return $this->isProduction() ? 'badge-danger' : 'badge-warning';
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        if (!$this->is_active) {
            return 'Inactive';
        }
        
        return $this->isProduction() ? 'Production' : 'Development';
    }
}
