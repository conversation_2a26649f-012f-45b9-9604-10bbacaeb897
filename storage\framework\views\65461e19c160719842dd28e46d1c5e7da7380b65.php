<?php $__env->startSection('title','DynamoDB Dashboard'); ?>

<?php $__env->startSection('main-content'); ?>
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">AWS Dashboard</h6>
    </div>
    <div class="card-body">
        <!-- Summary Cards Row 1 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">AWS Region</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($dashboardStats['aws_region']); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-globe fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Items</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($totalItems)); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Tables</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(collect($tablesData)->where('status', 'active')->count()); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.users')); ?>" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Users Count</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['users_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 2 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.app-notifications')); ?>" class="text-decoration-none">
                    <div class="card border-left-primary shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">App Notifications</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['app_notifications_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bell fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.certificates')); ?>" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Certificates</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['certificates_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-certificate fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.certificate-recipients')); ?>" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Certificate Recipients</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['certificate_recipients_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-share fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.delegates')); ?>" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Delegates</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['delegates_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 3 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.recipients')); ?>" class="text-decoration-none">
                    <div class="card border-left-danger shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Recipients</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['recipients_count'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-address-book fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['active'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-danger shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['inactive'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Auto-Renew Enabled</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['auto_renew'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-sync fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 4 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Expires Soon</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['expires_soon'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-primary shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Monthly Users</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['monthly'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Yearly Users</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['yearly'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="<?php echo e(route('dynamodb.subscriptions')); ?>" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($dashboardStats['subscriptions']['total'])); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Analytics Charts Section -->
        <div class="row mb-4">
            <!-- Subscription Status Pie Chart -->
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar pt-4 pb-2">
                            <canvas id="subscriptionStatusChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-square text-success"></i> Active (<?php echo e($dashboardStats['subscriptions']['active']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-danger"></i> Inactive (<?php echo e($dashboardStats['subscriptions']['inactive']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Duration Bar Chart -->
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Duration</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar pt-4 pb-2">
                            <canvas id="subscriptionDurationChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-square text-primary"></i> Monthly (<?php echo e($dashboardStats['subscriptions']['monthly']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-success"></i> Yearly (<?php echo e($dashboardStats['subscriptions']['yearly']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Distribution Bar Chart -->
            <div class="col-xl-4 col-lg-12 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Data Distribution</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar pt-4 pb-2">
                            <canvas id="dataDistributionChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-square text-primary"></i> Users (<?php echo e($dashboardStats['users_count']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-success"></i> Certificates (<?php echo e($dashboardStats['certificates_count']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-info"></i> Recipients (<?php echo e($dashboardStats['recipients_count']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-Renew and Expiry Analytics -->
        <div class="row mb-4">
            <!-- Auto-Renew Status -->
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Auto-Renew Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar pt-4 pb-2">
                            <canvas id="autoRenewChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-square text-info"></i> Auto-Renew (<?php echo e($dashboardStats['subscriptions']['auto_renew']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-warning"></i> Manual (<?php echo e($dashboardStats['subscriptions']['total'] - $dashboardStats['subscriptions']['auto_renew']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expiry Status -->
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Expiry Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar pt-4 pb-2">
                            <canvas id="expiryChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-square text-warning"></i> Expires Soon (<?php echo e($dashboardStats['subscriptions']['expires_soon']); ?>)
                            </span>
                            <span class="mr-2">
                                <i class="fas fa-square text-success"></i> Safe (<?php echo e($dashboardStats['subscriptions']['active'] - $dashboardStats['subscriptions']['expires_soon']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
<style>
    div.dataTables_wrapper div.dataTables_paginate{
        display: none;
    }
    .zoom {
        transition: transform .2s; /* Animation */
    }
    .zoom:hover {
        transform: scale(3.2);
    }
    .chart-bar {
        position: relative;
        height: 15rem;
        width: 100%;
        margin: 0 auto;
    }
    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 0.25rem solid #e74a3b !important;
    }
    .card-hover {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
    .text-decoration-none:hover {
        text-decoration: none !important;
    }
    .card-hover:hover .text-gray-800 {
        color: #5a5c69 !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Page level plugins -->
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    // Fallback if Chart.js fails to load
    if (typeof Chart === 'undefined') {
        console.error('Chart.js failed to load from CDN, trying alternative...');
        document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"><\/script>');
    }
</script>

<!-- Page level custom scripts -->
<script src="<?php echo e(asset('backend/js/demo/datatables-demo.js')); ?>"></script>
<script>
    $('#dataTable').DataTable({
        "columnDefs": [
            {
                "orderable": false,
                "targets": [5]
            }
        ]
    });

    // Wait for DOM to be fully loaded
    $(document).ready(function() {
        // Add loading indicators
        $('.chart-bar').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading chart...</div>');

        // Wait a bit for Chart.js to fully load
        setTimeout(function() {
            // Chart.js Configuration
            if (typeof Chart !== 'undefined') {
                Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
                Chart.defaults.global.defaultFontColor = '#858796';

                // Initialize all charts
                initializeCharts();
            } else {
                console.error('Chart.js not loaded');
                $('.chart-bar').html('<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> Chart.js failed to load</div>');
            }
        }, 500);
    });

    function initializeCharts() {
        try {
            // Subscription Status Bar Chart
            var ctx1 = document.getElementById("subscriptionStatusChart");
            if (ctx1) {
                new Chart(ctx1, {
                    type: 'bar',
                    data: {
                        labels: ["Active", "Inactive"],
                        datasets: [{
                            label: 'Subscriptions',
                            data: [<?php echo e($dashboardStats['subscriptions']['active']); ?>, <?php echo e($dashboardStats['subscriptions']['inactive']); ?>],
                            backgroundColor: ['#1cc88a', '#e74a3b'],
                            hoverBackgroundColor: ['#17a673', '#e02d1b'],
                            borderColor: ['#1cc88a', '#e74a3b'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: { left: 10, right: 25, top: 25, bottom: 0 }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: { display: false, drawBorder: false },
                                ticks: { maxTicksLimit: 6 },
                                maxBarThickness: 50
                            }],
                            yAxes: [{
                                ticks: { min: 0, maxTicksLimit: 5, padding: 10 },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }]
                        },
                        legend: { display: false },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10
                        }
                    }
                });
            }

            // Subscription Duration Bar Chart
            var ctx2 = document.getElementById("subscriptionDurationChart");
            if (ctx2) {
                new Chart(ctx2, {
                    type: 'bar',
                    data: {
                        labels: ["Monthly", "Yearly"],
                        datasets: [{
                            label: 'Duration',
                            data: [<?php echo e($dashboardStats['subscriptions']['monthly']); ?>, <?php echo e($dashboardStats['subscriptions']['yearly']); ?>],
                            backgroundColor: ['#4e73df', '#1cc88a'],
                            hoverBackgroundColor: ['#2e59d9', '#17a673'],
                            borderColor: ['#4e73df', '#1cc88a'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: { left: 10, right: 25, top: 25, bottom: 0 }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: { display: false, drawBorder: false },
                                ticks: { maxTicksLimit: 6 },
                                maxBarThickness: 50
                            }],
                            yAxes: [{
                                ticks: { min: 0, maxTicksLimit: 5, padding: 10 },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }]
                        },
                        legend: { display: false },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10
                        }
                    }
                });
            }

            // Data Distribution Bar Chart
            var ctx3 = document.getElementById("dataDistributionChart");
            if (ctx3) {
                new Chart(ctx3, {
                    type: 'bar',
                    data: {
                        labels: ["Users", "Certificates", "Recipients", "Delegates", "Notifications"],
                        datasets: [{
                            label: 'Count',
                            data: [
                                <?php echo e($dashboardStats['users_count']); ?>,
                                <?php echo e($dashboardStats['certificates_count']); ?>,
                                <?php echo e($dashboardStats['recipients_count']); ?>,
                                <?php echo e($dashboardStats['delegates_count']); ?>,
                                <?php echo e($dashboardStats['app_notifications_count']); ?>

                            ],
                            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#e02d1b'],
                            borderColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: { left: 10, right: 25, top: 25, bottom: 0 }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: { display: false, drawBorder: false },
                                ticks: { maxTicksLimit: 6 },
                                maxBarThickness: 30
                            }],
                            yAxes: [{
                                ticks: { min: 0, maxTicksLimit: 5, padding: 10 },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }]
                        },
                        legend: { display: false },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10
                        }
                    }
                });
            }

            // Auto-Renew Status Bar Chart
            var ctx4 = document.getElementById("autoRenewChart");
            if (ctx4) {
                new Chart(ctx4, {
                    type: 'bar',
                    data: {
                        labels: ["Auto-Renew", "Manual"],
                        datasets: [{
                            label: 'Renewal Type',
                            data: [
                                <?php echo e($dashboardStats['subscriptions']['auto_renew']); ?>,
                                <?php echo e($dashboardStats['subscriptions']['total'] - $dashboardStats['subscriptions']['auto_renew']); ?>

                            ],
                            backgroundColor: ['#36b9cc', '#f6c23e'],
                            hoverBackgroundColor: ['#2c9faf', '#dda20a'],
                            borderColor: ['#36b9cc', '#f6c23e'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: { left: 10, right: 25, top: 25, bottom: 0 }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: { display: false, drawBorder: false },
                                ticks: { maxTicksLimit: 6 },
                                maxBarThickness: 50
                            }],
                            yAxes: [{
                                ticks: { min: 0, maxTicksLimit: 5, padding: 10 },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }]
                        },
                        legend: { display: false },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10
                        }
                    }
                });
            }

            // Expiry Status Bar Chart
            var ctx5 = document.getElementById("expiryChart");
            if (ctx5) {
                new Chart(ctx5, {
                    type: 'bar',
                    data: {
                        labels: ["Expires Soon", "Safe"],
                        datasets: [{
                            label: 'Expiry Status',
                            data: [
                                <?php echo e($dashboardStats['subscriptions']['expires_soon']); ?>,
                                <?php echo e($dashboardStats['subscriptions']['active'] - $dashboardStats['subscriptions']['expires_soon']); ?>

                            ],
                            backgroundColor: ['#f6c23e', '#1cc88a'],
                            hoverBackgroundColor: ['#dda20a', '#17a673'],
                            borderColor: ['#f6c23e', '#1cc88a'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        layout: {
                            padding: { left: 10, right: 25, top: 25, bottom: 0 }
                        },
                        scales: {
                            xAxes: [{
                                gridLines: { display: false, drawBorder: false },
                                ticks: { maxTicksLimit: 6 },
                                maxBarThickness: 50
                            }],
                            yAxes: [{
                                ticks: { min: 0, maxTicksLimit: 5, padding: 10 },
                                gridLines: {
                                    color: "rgb(234, 236, 244)",
                                    zeroLineColor: "rgb(234, 236, 244)",
                                    drawBorder: false,
                                    borderDash: [2],
                                    zeroLineBorderDash: [2]
                                }
                            }]
                        },
                        legend: { display: false },
                        tooltips: {
                            titleMarginBottom: 10,
                            titleFontColor: '#6e707e',
                            titleFontSize: 14,
                            backgroundColor: "rgb(255,255,255)",
                            bodyFontColor: "#858796",
                            borderColor: '#dddfeb',
                            borderWidth: 1,
                            xPadding: 15,
                            yPadding: 15,
                            displayColors: false,
                            caretPadding: 10
                        }
                    }
                });
            }

        } catch (error) {
            console.error('Error initializing charts:', error);
        }
    }


</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/dashboard.blade.php ENDPATH**/ ?>