<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Environment;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EnvironmentController extends Controller
{
    /**
     * Display a listing of environments
     */
    public function index()
    {
        $environments = Environment::orderBy('created_at', 'desc')->get();
        
        return view('backend.environments.index', compact('environments'));
    }

    /**
     * Show the form for creating a new environment
     */
    public function create()
    {
        return view('backend.environments.create');
    }

    /**
     * Store a newly created environment
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:environments,name',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'config' => 'nullable|json'
        ]);

        try {
            $environment = Environment::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
                'is_default' => $request->has('is_default'),
                'config' => $request->config ? json_decode($request->config, true) : null
            ]);

            return redirect()->route('environments.index')
                ->with('success', 'Environment created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create environment: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified environment
     */
    public function show(Environment $environment)
    {
        return view('backend.environments.show', compact('environment'));
    }

    /**
     * Show the form for editing the specified environment
     */
    public function edit(Environment $environment)
    {
        return view('backend.environments.edit', compact('environment'));
    }

    /**
     * Update the specified environment
     */
    public function update(Request $request, Environment $environment)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:environments,name,' . $environment->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'config' => 'nullable|json'
        ]);

        try {
            $environment->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
                'is_default' => $request->has('is_default'),
                'config' => $request->config ? json_decode($request->config, true) : null
            ]);

            return redirect()->route('environments.index')
                ->with('success', 'Environment updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update environment: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified environment
     */
    public function destroy(Environment $environment)
    {
        try {
            // Prevent deletion of default environment
            if ($environment->is_default) {
                return redirect()->back()
                    ->with('error', 'Cannot delete the default environment!');
            }

            $environment->delete();

            return redirect()->route('environments.index')
                ->with('success', 'Environment deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete environment: ' . $e->getMessage());
        }
    }

    /**
     * Toggle environment status
     */
    public function toggleStatus(Environment $environment)
    {
        try {
            $environment->update([
                'is_active' => !$environment->is_active
            ]);

            $status = $environment->is_active ? 'activated' : 'deactivated';
            
            return response()->json([
                'success' => true,
                'message' => "Environment {$status} successfully!",
                'is_active' => $environment->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle environment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set as default environment
     */
    public function setDefault(Environment $environment)
    {
        try {
            $environment->update(['is_default' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Environment set as default successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set default environment: ' . $e->getMessage()
            ], 500);
        }
    }
}
