<?php $__env->startSection('title','Plan Detail || Admin Panel'); ?>

<?php $__env->startSection('main-content'); ?>
<div class="card">
    <h5 class="card-header">Plan Details</h5>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><?php echo e($plan->name); ?></h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped table-hover">
                            <tr>
                                <th>ID:</th>
                                <td><?php echo e($plan->id); ?></td>
                            </tr>
                            <tr>
                                <th>Name:</th>
                                <td><?php echo e($plan->name); ?></td>
                            </tr>
                            <tr>
                                <th>Duration:</th>
                                <td><?php echo e($plan->duration); ?></td>
                            </tr>
                            <tr>
                                <th>Number of Allowed Fax:</th>
                                <td><?php echo e(number_format($plan->no_of_allowed_fax)); ?></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php if($plan->status=='active'): ?>
                                        <span class="badge badge-success"><?php echo e($plan->status); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-warning"><?php echo e($plan->status); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Created By:</th>
                                <td><?php echo e($plan->creator ? $plan->creator->name : 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <th>Updated By:</th>
                                <td><?php echo e($plan->updater ? $plan->updater->name : 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <th>Created Date:</th>
                                <td><?php echo e($plan->created_at->format('M d, Y h:i A')); ?></td>
                            </tr>
                            <tr>
                                <th>Updated Date:</th>
                                <td><?php echo e($plan->updated_at->format('M d, Y h:i A')); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('plans.edit',$plan->id)); ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit Plan
                            </a>
                            <a href="<?php echo e(route('plans.index')); ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                            <form action="<?php echo e(route('plans.destroy',$plan->id)); ?>" method="POST" style="display:inline;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger btn-sm w-100" onclick="return confirm('Are you sure you want to delete this plan?')">
                                    <i class="fas fa-trash"></i> Delete Plan
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <?php if($plan->status == 'active'): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Plan Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>Active Plan</strong><br>
                            This plan is currently active and available for users.
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Plan Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Inactive Plan</strong><br>
                            This plan is currently inactive and not available for users.
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/plans/show.blade.php ENDPATH**/ ?>