<?php $__env->startSection('title','Edit Plan || Admin Panel'); ?>

<?php $__env->startSection('main-content'); ?>

<div class="card">
    <h5 class="card-header">Edit Plan</h5>
    <div class="card-body">
      <form method="post" action="<?php echo e(route('plans.update',$plan->id)); ?>">
        <?php echo csrf_field(); ?> 
        <?php echo method_field('PATCH'); ?>
        <div class="form-group">
          <label for="inputTitle" class="col-form-label">Name <span class="text-danger">*</span></label>
          <input id="inputTitle" type="text" name="name" placeholder="Enter plan name"  value="<?php echo e($plan->name); ?>" class="form-control">
          <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger"><?php echo e($message); ?></span>
          <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="form-group">
          <label for="duration" class="col-form-label">Duration <span class="text-danger">*</span></label>
          <select name="duration" class="form-control">
              <option value="">--Select Duration--</option>
              <option value="1 month" <?php echo e($plan->duration=='1 month' ? 'selected' : ''); ?>>1 Month</option>
              <option value="3 months" <?php echo e($plan->duration=='3 months' ? 'selected' : ''); ?>>3 Months</option>
              <option value="6 months" <?php echo e($plan->duration=='6 months' ? 'selected' : ''); ?>>6 Months</option>
              <option value="1 year" <?php echo e($plan->duration=='1 year' ? 'selected' : ''); ?>>1 Year</option>
              <option value="2 years" <?php echo e($plan->duration=='2 years' ? 'selected' : ''); ?>>2 Years</option>
              <option value="lifetime" <?php echo e($plan->duration=='lifetime' ? 'selected' : ''); ?>>Lifetime</option>
          </select>
          <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger"><?php echo e($message); ?></span>
          <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="form-group">
          <label for="no_of_allowed_fax" class="col-form-label">Number of Allowed Fax <span class="text-danger">*</span></label>
          <input id="no_of_allowed_fax" type="number" name="no_of_allowed_fax" placeholder="Enter number of allowed fax" value="<?php echo e($plan->no_of_allowed_fax); ?>" class="form-control" min="0">
          <?php $__errorArgs = ['no_of_allowed_fax'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger"><?php echo e($message); ?></span>
          <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="form-group">
          <label for="status" class="col-form-label">Status <span class="text-danger">*</span></label>
          <select name="status" class="form-control">
              <option value="active" <?php echo e($plan->status=='active' ? 'selected' : ''); ?>>Active</option>
              <option value="inactive" <?php echo e($plan->status=='inactive' ? 'selected' : ''); ?>>Inactive</option>
          </select>
          <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
          <span class="text-danger"><?php echo e($message); ?></span>
          <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="form-group mb-3">
           <button class="btn btn-success" type="submit">Update</button>
        </div>
      </form>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('backend/summernote/summernote.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="<?php echo e(asset('backend/summernote/summernote.min.js')); ?>"></script>
<script>
    $('#lfm').filemanager('image');

    $(document).ready(function() {
    $('#description').summernote({
      placeholder: "Write short description.....",
        tabsize: 2,
        height: 150
    });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/plans/edit.blade.php ENDPATH**/ ?>