@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Add Environment</h5>
    <div class="card-body">
        <form method="post" action="{{ route('environments.store') }}">
            {{ csrf_field() }}
            
            <div class="form-group">
                <label for="inputTitle" class="col-form-label">Name <span class="text-danger">*</span></label>
                <input id="inputTitle" type="text" name="name" placeholder="Enter environment name" value="{{ old('name') }}" class="form-control">
                @error('name')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="description" class="col-form-label">Description</label>
                <textarea class="form-control" id="description" name="description" placeholder="Enter environment description">{{ old('description') }}</textarea>
                @error('description')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="config" class="col-form-label">Configuration (JSON)</label>
                <textarea class="form-control" id="config" name="config" rows="6" placeholder='{"debug": true, "log_level": "debug", "cache_enabled": false}'>{{ old('config') }}</textarea>
                @error('config')
                <span class="text-danger">{{ $message }}</span>
                @enderror
                <small class="form-text text-muted">Enter configuration as valid JSON format</small>
            </div>

            <div class="form-group">
                <label class="col-form-label">Status & Settings</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="1" id="is_active" name="is_active" {{ old('is_active') ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                        Active
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="1" id="is_default" name="is_default" {{ old('is_default') ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_default">
                        Set as Default Environment
                    </label>
                </div>
            </div>

            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button class="btn btn-success" type="submit">Submit</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/summernote/summernote.min.css') }}">
<style>
    .form-check {
        margin-bottom: 10px;
    }
    .form-check-label {
        margin-left: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{ asset('backend/summernote/summernote.min.js') }}"></script>
<script>
    $('#lfm').filemanager('image');

    $(document).ready(function() {
        $('#description').summernote({
            placeholder: "Write short description.....",
            tabsize: 2,
            height: 120,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'help']]
            ]
        });

        // JSON validation for config field
        $('#config').on('blur', function() {
            var configValue = $(this).val().trim();
            if (configValue && configValue !== '') {
                try {
                    JSON.parse(configValue);
                    $(this).removeClass('is-invalid').addClass('is-valid');
                    $('.config-error').remove();
                } catch (e) {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                    if (!$('.config-error').length) {
                        $(this).after('<div class="config-error text-danger small mt-1">Invalid JSON format</div>');
                    }
                }
            } else {
                $(this).removeClass('is-invalid is-valid');
                $('.config-error').remove();
            }
        });

        // Auto-uncheck other default if this is checked
        $('#is_default').change(function() {
            if ($(this).is(':checked')) {
                swal({
                    title: "Set as Default?",
                    text: "This will make this environment the default one. Continue?",
                    icon: "info",
                    buttons: true,
                })
                .then((willSet) => {
                    if (!willSet) {
                        $(this).prop('checked', false);
                    }
                });
            }
        });
    });
</script>
@endpush
