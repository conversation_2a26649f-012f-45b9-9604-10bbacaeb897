@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Add Environment</h5>
    <div class="card-body">
        <form method="post" action="{{ route('environments.store') }}">
            {{ csrf_field() }}
            
            <div class="form-group">
                <label for="inputEnv" class="col-form-label">Environment <span class="text-danger">*</span></label>
                <input id="inputEnv" type="text" name="env" placeholder="Enter environment name (e.g., prod, dev, staging)" value="{{ old('env') }}" class="form-control">
                @error('env')
                <span class="text-danger">{{ $message }}</span>
                @enderror
                <small class="form-text text-muted">Enter a short environment identifier (e.g., prod, dev, staging, test)</small>
            </div>

            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button class="btn btn-success" type="submit">Submit</button>
                <a href="{{ route('environments.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<style>
    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Auto-convert to lowercase
        $('#inputEnv').on('input', function() {
            $(this).val($(this).val().toLowerCase());
        });
    });
</script>
@endpush
