<?php $__env->startSection('title','Subscriptions - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Subscriptions (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($subscriptions) > 0): ?>
                <table class="table table-bordered" id="subscription-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Plan Name</th>
                            <th>Status</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <code style="font-size: 10px;"><?php echo e(substr($subscription['id'] ?? 'N/A', 0, 20)); ?>...</code>
                            </td>
                            <td>
                                <span class="badge badge-info"><?php echo e($subscription['__typename'] ?? 'N/A'); ?></span>
                            </td>
                            <td>
                                <strong><?php echo e($subscription['planName'] ?? 'N/A'); ?></strong>
                            </td>
                            <td>
                                <?php if(isset($subscription['status'])): ?>
                                    <?php switch($subscription['status']):
                                        case ('ACTIVE'): ?>
                                            <span class="badge badge-success"><?php echo e($subscription['status']); ?></span>
                                            <?php break; ?>
                                        <?php case ('INACTIVE'): ?>
                                            <span class="badge badge-danger"><?php echo e($subscription['status']); ?></span>
                                            <?php break; ?>
                                        <?php case ('EXPIRED'): ?>
                                            <span class="badge badge-warning"><?php echo e($subscription['status']); ?></span>
                                            <?php break; ?>
                                        <?php case ('CANCELLED'): ?>
                                            <span class="badge badge-secondary"><?php echo e($subscription['status']); ?></span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($subscription['status']); ?></span>
                                    <?php endswitch; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['startDate'] ?? null)); ?>

                            </td>
                            <td>
                                <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['endDate'] ?? null)); ?>

                            </td>
                            <td>
                                <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['createdAt'] ?? null)); ?>

                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No subscriptions found!</h6>
                    <p>There are no subscriptions in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#subscription-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 7, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/subscriptions.blade.php ENDPATH**/ ?>