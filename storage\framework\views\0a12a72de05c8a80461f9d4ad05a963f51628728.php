<?php $__env->startSection('title','Subscriptions - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Subscriptions (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($subscriptions) > 0): ?>
                <table class="table table-bordered" id="subscription-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Subscription Info</th>
                            <th>Owner</th>
                            <th>Amount & Currency</th>
                            <th>Status</th>
                            <th>Duration & Dates</th>
                            <th>Payment State</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row" data-subscription-id="<?php echo e($subscription['id'] ?? ''); ?>">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <div class="subscription-info">
                                    <strong><?php echo e($subscription['category'] ?? $subscription['type'] ?? 'N/A'); ?></strong>
                                    <?php if(isset($subscription['store'])): ?>
                                        <br><small class="text-muted">Store: <?php echo e($subscription['store']); ?></small>
                                    <?php endif; ?>
                                    <?php if(isset($subscription['faxCount'])): ?>
                                        <br><small class="text-muted">Fax Count: <?php echo e($subscription['faxCount']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo e($subscription['owner'] ?? 'N/A'); ?></strong>
                                <?php if(isset($subscription['userID'])): ?>
                                    <br><small class="text-muted">User: <code><?php echo e(substr($subscription['userID'], 0, 15)); ?>...</code></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="amount-info">
                                    <?php if(isset($subscription['amount'])): ?>
                                        <strong><?php echo e($subscription['currency'] ?? '$'); ?><?php echo e(number_format($subscription['amount'], 2)); ?></strong>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                    <?php if(isset($subscription['duration'])): ?>
                                        <br><small class="text-muted"><?php echo e($subscription['duration']); ?> days</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="status-info">
                                    <?php if(isset($subscription['isActive'])): ?>
                                        <?php if($subscription['isActive'] === true || $subscription['isActive'] === 'true'): ?>
                                            <span class="badge badge-success"><i class="fas fa-check-circle"></i> ACTIVE</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger"><i class="fas fa-times-circle"></i> INACTIVE</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">N/A</span>
                                    <?php endif; ?>
                                    <?php if(isset($subscription['autoRenewing'])): ?>
                                        <br>
                                        <?php if($subscription['autoRenewing'] === true || $subscription['autoRenewing'] === 'true'): ?>
                                            <span class="badge badge-info"><i class="fas fa-sync"></i> Auto-Renew</span>
                                        <?php else: ?>
                                            <span class="badge badge-warning"><i class="fas fa-ban"></i> No Auto-Renew</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    <small><strong>Start:</strong> <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['startDate'] ?? null)); ?></small>
                                    <br><small><strong>End:</strong> <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['endDate'] ?? null)); ?></small>
                                    <?php if(isset($subscription['gracePeriodEndDate'])): ?>
                                        <br><small><strong>Grace:</strong> <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['gracePeriodEndDate'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if(isset($subscription['paymentState'])): ?>
                                    <?php switch($subscription['paymentState']):
                                        case ('PAID'): ?>
                                            <span class="badge badge-success"><i class="fas fa-check"></i> <?php echo e($subscription['paymentState']); ?></span>
                                            <?php break; ?>
                                        <?php case ('PENDING'): ?>
                                            <span class="badge badge-warning"><i class="fas fa-clock"></i> <?php echo e($subscription['paymentState']); ?></span>
                                            <?php break; ?>
                                        <?php case ('FAILED'): ?>
                                            <span class="badge badge-danger"><i class="fas fa-times"></i> <?php echo e($subscription['paymentState']); ?></span>
                                            <?php break; ?>
                                        <?php case ('REFUNDED'): ?>
                                            <span class="badge badge-info"><i class="fas fa-undo"></i> <?php echo e($subscription['paymentState']); ?></span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($subscription['paymentState']); ?></span>
                                    <?php endswitch; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-subscription='<?php echo json_encode($subscription, 15, 512) ?>'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if(isset($subscription['receiptData']) && $subscription['receiptData']): ?>
                                    <button class="btn btn-primary btn-sm view-receipt" data-toggle="tooltip" title="View Receipt" data-receipt="<?php echo e($subscription['receiptData']); ?>">
                                        <i class="fas fa-receipt"></i>
                                    </button>
                                <?php endif; ?>
                                <?php if(isset($subscription['renewalHistory']) && $subscription['renewalHistory']): ?>
                                    <button class="btn btn-success btn-sm view-history" data-toggle="tooltip" title="View Renewal History" data-history="<?php echo e($subscription['renewalHistory']); ?>">
                                        <i class="fas fa-history"></i>
                                    </button>
                                <?php endif; ?>
                                <?php if(isset($subscription['cancellationDate'])): ?>
                                    <span class="btn btn-warning btn-sm" data-toggle="tooltip" title="Cancelled">
                                        <i class="fas fa-ban"></i>
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No subscriptions found!</h6>
                    <p>There are no subscriptions in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Subscription Details Modal -->
<div class="modal fade" id="subscriptionModal" tabindex="-1" role="dialog" aria-labelledby="subscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subscriptionModalLabel">Subscription Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="subscriptionDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Receipt Data Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1" role="dialog" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiptModalLabel">Receipt Data</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="receiptContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Renewal History Modal -->
<div class="modal fade" id="historyModal" tabindex="-1" role="dialog" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="historyModalLabel">Renewal History</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="historyContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details, .view-receipt, .view-history {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
        margin: 2px;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
    .subscription-info, .amount-info, .status-info, .date-info {
        min-height: 40px;
    }
    .receipt-data, .history-data {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        white-space: pre-wrap;
        word-break: break-all;
    }
    .amount-highlight {
        font-size: 1.1em;
        font-weight: bold;
        color: #28a745;
    }
    .trial-badge {
        background-color: #17a2b8;
        color: white;
    }
    .intro-price-badge {
        background-color: #fd7e14;
        color: white;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#subscription-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 5, "desc" ]], // Order by duration & dates
            "columnDefs": [
                {
                    "targets": [7], // Actions column
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var subscription = $(this).data('subscription');
            showSubscriptionDetails(subscription);
        });

        // Handle view receipt button click
        $('.view-receipt').on('click', function() {
            var receipt = $(this).data('receipt');
            showReceiptData(receipt);
        });

        // Handle view history button click
        $('.view-history').on('click', function() {
            var history = $(this).data('history');
            showRenewalHistory(history);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showSubscriptionDetails(subscription) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${subscription.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${subscription.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${subscription.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>User ID:</th>
                            <td><code>${subscription.userID || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Category:</th>
                            <td><strong>${subscription.category || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><strong>${subscription.type || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Store:</th>
                            <td><strong>${subscription.store || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Amount:</th>
                            <td class="amount-highlight">${subscription.currency || '$'}${subscription.amount ? Number(subscription.amount).toFixed(2) : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Currency:</th>
                            <td><strong>${subscription.currency || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Duration:</th>
                            <td><strong>${subscription.duration || 'N/A'} ${subscription.duration ? 'days' : ''}</strong></td>
                        </tr>
                        <tr>
                            <th>Fax Count:</th>
                            <td><strong>${subscription.faxCount || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Active Status:</th>
                            <td>${getActiveStatusBadge(subscription.isActive)}</td>
                        </tr>
                        <tr>
                            <th>Auto Renewing:</th>
                            <td>${getAutoRenewBadge(subscription.autoRenewing)}</td>
                        </tr>
                        <tr>
                            <th>Payment State:</th>
                            <td>${getPaymentStateBadge(subscription.paymentState)}</td>
                        </tr>
                        <tr>
                            <th>Trial Period:</th>
                            <td>${subscription.trialPeriod ? `<span class="badge trial-badge">${subscription.trialPeriod} days</span>` : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Introductory Price Used:</th>
                            <td>${subscription.introductoryPriceUsed ? '<span class="badge intro-price-badge">Yes</span>' : '<span class="badge badge-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <th>Start Date:</th>
                            <td>${subscription.startDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>End Date:</th>
                            <td>${subscription.endDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Grace Period End Date:</th>
                            <td>${subscription.gracePeriodEndDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Cancellation Date:</th>
                            <td>${subscription.cancellationDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${subscription.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${subscription.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Latest Transaction ID:</th>
                            <td><code>${subscription.latestTransactionId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Original Transaction ID:</th>
                            <td><code>${subscription.originalTransactionId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Purchase Token:</th>
                            <td><code>${subscription.purchaseToken || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Receipt Data:</th>
                            <td>${subscription.receiptData ? '<span class="badge badge-success">Available</span>' : '<span class="badge badge-secondary">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>Renewal History:</th>
                            <td>${subscription.renewalHistory ? '<span class="badge badge-success">Available</span>' : '<span class="badge badge-secondary">Not Available</span>'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#subscriptionDetails').html(detailsHtml);
        $('#subscriptionModal').modal('show');
    }

    function showReceiptData(receiptData) {
        var receiptHtml = `
            <div class="receipt-data">
                <h6><i class="fas fa-receipt"></i> Receipt Information</h6>
                ${receiptData}
            </div>
        `;

        $('#receiptContent').html(receiptHtml);
        $('#receiptModal').modal('show');
    }

    function showRenewalHistory(historyData) {
        var historyHtml = `
            <div class="history-data">
                <h6><i class="fas fa-history"></i> Renewal History</h6>
                ${historyData}
            </div>
        `;

        $('#historyContent').html(historyHtml);
        $('#historyModal').modal('show');
    }

    function getActiveStatusBadge(isActive) {
        if (isActive === true || isActive === 'true') {
            return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> ACTIVE</span>';
        } else if (isActive === false || isActive === 'false') {
            return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> INACTIVE</span>';
        } else {
            return '<span class="badge badge-secondary">N/A</span>';
        }
    }

    function getAutoRenewBadge(autoRenewing) {
        if (autoRenewing === true || autoRenewing === 'true') {
            return '<span class="badge badge-info"><i class="fas fa-sync"></i> Auto-Renew Enabled</span>';
        } else if (autoRenewing === false || autoRenewing === 'false') {
            return '<span class="badge badge-warning"><i class="fas fa-ban"></i> Auto-Renew Disabled</span>';
        } else {
            return '<span class="badge badge-secondary">N/A</span>';
        }
    }

    function getPaymentStateBadge(paymentState) {
        if (!paymentState) return '<span class="badge badge-secondary">N/A</span>';

        switch(paymentState) {
            case 'PAID':
                return '<span class="badge badge-success"><i class="fas fa-check"></i> ' + paymentState + '</span>';
            case 'PENDING':
                return '<span class="badge badge-warning"><i class="fas fa-clock"></i> ' + paymentState + '</span>';
            case 'FAILED':
                return '<span class="badge badge-danger"><i class="fas fa-times"></i> ' + paymentState + '</span>';
            case 'REFUNDED':
                return '<span class="badge badge-info"><i class="fas fa-undo"></i> ' + paymentState + '</span>';
            default:
                return '<span class="badge badge-secondary">' + paymentState + '</span>';
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/subscriptions.blade.php ENDPATH**/ ?>