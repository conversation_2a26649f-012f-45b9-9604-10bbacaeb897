<?php $__env->startSection('title','Subscriptions - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Subscriptions (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($subscriptions) > 0): ?>
                <table class="table table-bordered" id="subscription-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Auto Renewing</th>
                            <th>Category</th>
                            <th>Cancellation Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <code style="font-size: 10px;"><?php echo e(substr($subscription['id'] ?? 'N/A', 0, 20)); ?>...</code>
                            </td>
                            <td>
                                <span class="badge badge-info"><?php echo e($subscription['__typename'] ?? 'N/A'); ?></span>
                            </td>
                            <td>
                                <?php if(isset($subscription['amount']) && $subscription['amount'] !== null): ?>
                                    <span class="badge badge-success">$<?php echo e(number_format($subscription['amount'], 2)); ?></span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">null</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($subscription['autoRenewing'])): ?>
                                    <?php if($subscription['autoRenewing'] === true): ?>
                                        <span class="badge badge-success">Yes</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">No</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($subscription['category'])): ?>
                                    <span class="badge badge-primary"><?php echo e($subscription['category']); ?></span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($subscription['cancellationDate']) && $subscription['cancellationDate'] !== null): ?>
                                    <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($subscription['cancellationDate'])); ?>

                                <?php else: ?>
                                    <span class="badge badge-secondary">null</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No subscriptions found!</h6>
                    <p>There are no subscriptions in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Subscription Details Modal -->
<div class="modal fade" id="subscriptionModal" tabindex="-1" role="dialog" aria-labelledby="subscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subscriptionModalLabel">Subscription Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="subscriptionDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Page level plugins -->
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#subscription-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 0, "asc" ]],
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full subscription details
        $('tbody').on('click', '.clickable-row', function() {
            var data = $('#subscription-dataTable').DataTable().row(this).data();
            if (data) {
                showSubscriptionDetails(this);
            }
        });
    });

    function showSubscriptionDetails(row) {
        // Get the subscription data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var amount = $(cells[3]).text();
        var autoRenewing = $(cells[4]).find('.badge').text();
        var category = $(cells[5]).find('.badge').text();
        var cancellationDate = $(cells[6]).text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Amount:</th>
                            <td>${amount}</td>
                        </tr>
                        <tr>
                            <th>Auto Renewing:</th>
                            <td>${autoRenewing}</td>
                        </tr>
                        <tr>
                            <th>Category:</th>
                            <td>${category}</td>
                        </tr>
                        <tr>
                            <th>Cancellation Date:</th>
                            <td>${cancellationDate}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#subscriptionDetails').html(detailsHtml);
        $('#subscriptionModal').modal('show');
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/subscriptions.blade.php ENDPATH**/ ?>