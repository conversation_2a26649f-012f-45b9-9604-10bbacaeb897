@extends('backend.layouts.master')

@section('title','App Notifications - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">App Notifications ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($notifications) > 0)
                <table class="table table-bordered" id="notification-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Body</th>
                            <th>Name</th>
                            <th>Created At</th>
                            <th>Expiration Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($notifications as $index => $notification)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($notification['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $notification['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                    {{ substr($notification['body'] ?? 'N/A', 0, 50) }}
                                    @if(strlen($notification['body'] ?? '') > 50)...@endif
                                </div>
                            </td>
                            <td>{{ $notification['name'] ?? 'N/A' }}</td>
                            <td>
                                @if(isset($notification['createdAt']))
                                    {{ \Carbon\Carbon::parse($notification['createdAt'])->format('Y-m-d H:i:s') }}
                                @else
                                    N/A
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['expirationDate']))
                                    {{ \Carbon\Carbon::parse($notification['expirationDate'])->format('Y-m-d H:i:s') }}
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No notifications found!</h6>
                    <p>There are no app notifications in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Notification Details Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">Notification Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="notificationDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    .notification-body {
        max-width: 300px;
        word-wrap: break-word;
    }
    code {
        font-size: 11px;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#notification-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 5, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1, 3], // ID and Body columns
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full notification details
        $('tbody').on('click', 'tr', function() {
            var data = $('#notification-dataTable').DataTable().row(this).data();
            if (data) {
                showNotificationDetails(this);
            }
        });
    });

    function showNotificationDetails(row) {
        // Get the notification data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var body = $(cells[3]).text();
        var name = $(cells[4]).text();
        var createdAt = $(cells[5]).text();
        var expirationDate = $(cells[6]).text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td>${name}</td>
                        </tr>
                        <tr>
                            <th>Body:</th>
                            <td class="notification-body">${body}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${createdAt}</td>
                        </tr>
                        <tr>
                            <th>Expiration Date:</th>
                            <td>${expirationDate}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#notificationDetails').html(detailsHtml);
        $('#notificationModal').modal('show');
    }
</script>
@endpush
