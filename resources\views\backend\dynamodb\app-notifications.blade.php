@extends('backend.layouts.master')

@section('title','App Notifications - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">App Notifications ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($notifications) > 0)
                <table class="table table-bordered" id="notification-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Read</th>
                            <th>User ID</th>
                            <th>Remaining Days</th>
                            <th>Notification Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($notifications as $index => $notification)
                        <tr class="clickable-row" data-notification-id="{{ $notification['id'] ?? '' }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <strong>{{ $notification['title'] ?? $notification['name'] ?? 'N/A' }}</strong>
                                @if(isset($notification['body']))
                                    <br><small class="text-muted">{{ substr($notification['body'], 0, 60) }}{{ strlen($notification['body']) > 60 ? '...' : '' }}</small>
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['type']))
                                    <span class="badge badge-primary">{{ $notification['type'] }}</span>
                                @elseif(isset($notification['__typename']))
                                    <span class="badge badge-info">{{ $notification['__typename'] }}</span>
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['status']))
                                    @switch($notification['status'])
                                        @case('ACTIVE')
                                            <span class="badge badge-success">{{ $notification['status'] }}</span>
                                            @break
                                        @case('INACTIVE')
                                            <span class="badge badge-danger">{{ $notification['status'] }}</span>
                                            @break
                                        @case('PENDING')
                                            <span class="badge badge-warning">{{ $notification['status'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $notification['status'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['read']))
                                    @if($notification['read'] === true || $notification['read'] === 'true')
                                        <span class="badge badge-success"><i class="fas fa-check"></i> Read</span>
                                    @else
                                        <span class="badge badge-warning"><i class="fas fa-envelope"></i> Unread</span>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['userId']))
                                    <code style="font-size: 10px;">{{ substr($notification['userId'], 0, 15) }}...</code>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($notification['remainingDays']))
                                    @if($notification['remainingDays'] > 0)
                                        <span class="badge badge-info">{{ $notification['remainingDays'] }} days</span>
                                    @elseif($notification['remainingDays'] == 0)
                                        <span class="badge badge-warning">Today</span>
                                    @else
                                        <span class="badge badge-danger">Expired</span>
                                    @endif
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($notification['notificationDate'] ?? $notification['createdAt'] ?? null) }}
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-notification='@json($notification)'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if(isset($notification['url']) || isset($notification['tempUrl']))
                                    <a href="{{ $notification['url'] ?? $notification['tempUrl'] }}" target="_blank" class="btn btn-primary btn-sm" data-toggle="tooltip" title="Open Link">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No notifications found!</h6>
                    <p>There are no app notifications in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Notification Details Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">Notification Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="notificationDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    .notification-body {
        max-width: 300px;
        word-wrap: break-word;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#notification-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 7, "desc" ]], // Order by notification date
            "columnDefs": [
                {
                    "targets": [5, 8], // User ID and Actions columns
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var notification = $(this).data('notification');
            showNotificationDetails(notification);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showNotificationDetails(notification) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${notification.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${notification.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Title:</th>
                            <td><strong>${notification.title || notification.name || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Body:</th>
                            <td>${notification.body || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-primary">${notification.type || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>${getStatusBadge(notification.status)}</td>
                        </tr>
                        <tr>
                            <th>Read Status:</th>
                            <td>${getReadBadge(notification.read)}</td>
                        </tr>
                        <tr>
                            <th>User ID:</th>
                            <td><code>${notification.userId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Remaining Days:</th>
                            <td>${getRemainingDaysBadge(notification.remainingDays)}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${notification.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${notification.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Notification Date:</th>
                            <td>${notification.notificationDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Expiration Date:</th>
                            <td>${notification.expirationDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>URL:</th>
                            <td>${notification.url ? `<a href="${notification.url}" target="_blank">${notification.url}</a>` : 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Temp URL:</th>
                            <td>${notification.tempUrl ? `<a href="${notification.tempUrl}" target="_blank">${notification.tempUrl}</a>` : 'N/A'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#notificationDetails').html(detailsHtml);
        $('#notificationModal').modal('show');
    }

    function getStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'ACTIVE':
                return '<span class="badge badge-success">' + status + '</span>';
            case 'INACTIVE':
                return '<span class="badge badge-danger">' + status + '</span>';
            case 'PENDING':
                return '<span class="badge badge-warning">' + status + '</span>';
            default:
                return '<span class="badge badge-secondary">' + status + '</span>';
        }
    }

    function getReadBadge(read) {
        if (read === undefined || read === null) return '<span class="badge badge-secondary">N/A</span>';

        if (read === true || read === 'true') {
            return '<span class="badge badge-success"><i class="fas fa-check"></i> Read</span>';
        } else {
            return '<span class="badge badge-warning"><i class="fas fa-envelope"></i> Unread</span>';
        }
    }

    function getRemainingDaysBadge(days) {
        if (days === undefined || days === null) return '<span class="badge badge-secondary">N/A</span>';

        if (days > 0) {
            return '<span class="badge badge-info">' + days + ' days</span>';
        } else if (days == 0) {
            return '<span class="badge badge-warning">Today</span>';
        } else {
            return '<span class="badge badge-danger">Expired</span>';
        }
    }
</script>
@endpush
