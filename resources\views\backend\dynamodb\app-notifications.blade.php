@extends('backend.layouts.master')

@section('title','App Notifications - DynamoDB')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">App Notifications ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($notifications) > 0)
                <table class="table table-bordered" id="notification-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Body</th>
                            <th>Name</th>
                            <th>Created At</th>
                            <th>Expiration Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($notifications as $index => $notification)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($notification['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $notification['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                    {{ substr($notification['body'] ?? 'N/A', 0, 50) }}
                                    @if(strlen($notification['body'] ?? '') > 50)...@endif
                                </div>
                            </td>
                            <td>{{ $notification['name'] ?? 'N/A' }}</td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($notification['createdAt'] ?? null) }}
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($notification['expirationDate'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No notifications found!</h6>
                    <p>There are no app notifications in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    .notification-body {
        max-width: 300px;
        word-wrap: break-word;
    }
    code {
        font-size: 11px;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#notification-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 5, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1, 3], // ID and Body columns
                    "orderable": false
                }
            ]
        });
    });
</script>
@endpush
