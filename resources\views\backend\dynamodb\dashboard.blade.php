@extends('backend.layouts.master')

@section('title','DynamoDB Dashboard')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">AWS Dashboard</h6>
    </div>
    <div class="card-body">
        <!-- Summary Cards Row 1 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">AWS Region</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $dashboardStats['aws_region'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-globe fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Items</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalItems) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Tables</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ collect($tablesData)->where('status', 'active')->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.users') }}" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Users Count</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['users_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 2 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.app-notifications') }}" class="text-decoration-none">
                    <div class="card border-left-primary shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">App Notifications</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['app_notifications_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bell fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.certificates') }}" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Certificates</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['certificates_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-certificate fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.certificate-recipients') }}" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Certificate Recipients</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['certificate_recipients_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-share fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.delegates') }}" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Delegates</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['delegates_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 3 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.recipients') }}" class="text-decoration-none">
                    <div class="card border-left-danger shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Recipients</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['recipients_count']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-address-book fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['active']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-danger shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['inactive']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Auto-Renew Enabled</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['auto_renew']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-sync fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Summary Cards Row 4 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-warning shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Expires Soon</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['expires_soon']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-primary shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Monthly Users</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['monthly']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-success shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Yearly Users</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['yearly']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <a href="{{ route('dynamodb.subscriptions') }}" class="text-decoration-none">
                    <div class="card border-left-info shadow h-100 py-2 card-hover">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Subscriptions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardStats['subscriptions']['total']) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Analytics Charts Section -->
        <div class="row mb-4">
            <!-- Subscription Status Pie Chart -->
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="simple-chart pt-4 pb-2">
                            <div class="chart-item mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Active</span>
                                    <span class="chart-value text-success font-weight-bold">{{ $dashboardStats['subscriptions']['active'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: {{ $dashboardStats['subscriptions']['total'] > 0 ? ($dashboardStats['subscriptions']['active'] / $dashboardStats['subscriptions']['total']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="chart-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Inactive</span>
                                    <span class="chart-value text-danger font-weight-bold">{{ $dashboardStats['subscriptions']['inactive'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-danger" style="width: {{ $dashboardStats['subscriptions']['total'] > 0 ? ($dashboardStats['subscriptions']['inactive'] / $dashboardStats['subscriptions']['total']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Duration Bar Chart -->
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Duration</h6>
                    </div>
                    <div class="card-body">
                        <div class="simple-chart pt-4 pb-2">
                            <div class="chart-item mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Monthly</span>
                                    <span class="chart-value text-primary font-weight-bold">{{ $dashboardStats['subscriptions']['monthly'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-primary" style="width: {{ ($dashboardStats['subscriptions']['monthly'] + $dashboardStats['subscriptions']['yearly']) > 0 ? ($dashboardStats['subscriptions']['monthly'] / ($dashboardStats['subscriptions']['monthly'] + $dashboardStats['subscriptions']['yearly'])) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="chart-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Yearly</span>
                                    <span class="chart-value text-success font-weight-bold">{{ $dashboardStats['subscriptions']['yearly'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: {{ ($dashboardStats['subscriptions']['monthly'] + $dashboardStats['subscriptions']['yearly']) > 0 ? ($dashboardStats['subscriptions']['yearly'] / ($dashboardStats['subscriptions']['monthly'] + $dashboardStats['subscriptions']['yearly'])) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Distribution Bar Chart -->
            <div class="col-xl-4 col-lg-12 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Data Distribution</h6>
                    </div>
                    <div class="card-body">
                        <div class="simple-chart pt-2 pb-2">
                            @php
                                $totalData = $dashboardStats['users_count'] + $dashboardStats['certificates_count'] + $dashboardStats['recipients_count'] + $dashboardStats['delegates_count'] + $dashboardStats['app_notifications_count'];
                            @endphp

                            <div class="chart-item mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label small">Users</span>
                                    <span class="chart-value text-primary font-weight-bold small">{{ $dashboardStats['users_count'] }}</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-primary" style="width: {{ $totalData > 0 ? ($dashboardStats['users_count'] / $totalData) * 100 : 0 }}%"></div>
                                </div>
                            </div>

                            <div class="chart-item mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label small">Certificates</span>
                                    <span class="chart-value text-success font-weight-bold small">{{ $dashboardStats['certificates_count'] }}</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-success" style="width: {{ $totalData > 0 ? ($dashboardStats['certificates_count'] / $totalData) * 100 : 0 }}%"></div>
                                </div>
                            </div>

                            <div class="chart-item mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label small">Recipients</span>
                                    <span class="chart-value text-info font-weight-bold small">{{ $dashboardStats['recipients_count'] }}</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-info" style="width: {{ $totalData > 0 ? ($dashboardStats['recipients_count'] / $totalData) * 100 : 0 }}%"></div>
                                </div>
                            </div>

                            <div class="chart-item mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label small">Delegates</span>
                                    <span class="chart-value text-warning font-weight-bold small">{{ $dashboardStats['delegates_count'] }}</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-warning" style="width: {{ $totalData > 0 ? ($dashboardStats['delegates_count'] / $totalData) * 100 : 0 }}%"></div>
                                </div>
                            </div>

                            <div class="chart-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label small">Notifications</span>
                                    <span class="chart-value text-danger font-weight-bold small">{{ $dashboardStats['app_notifications_count'] }}</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-danger" style="width: {{ $totalData > 0 ? ($dashboardStats['app_notifications_count'] / $totalData) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-Renew and Expiry Analytics -->
        <div class="row mb-4">
            <!-- Auto-Renew Status -->
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Auto-Renew Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="simple-chart pt-4 pb-2">
                            <div class="chart-item mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Auto-Renew</span>
                                    <span class="chart-value text-info font-weight-bold">{{ $dashboardStats['subscriptions']['auto_renew'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-info" style="width: {{ $dashboardStats['subscriptions']['total'] > 0 ? ($dashboardStats['subscriptions']['auto_renew'] / $dashboardStats['subscriptions']['total']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="chart-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Manual</span>
                                    <span class="chart-value text-warning font-weight-bold">{{ $dashboardStats['subscriptions']['total'] - $dashboardStats['subscriptions']['auto_renew'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-warning" style="width: {{ $dashboardStats['subscriptions']['total'] > 0 ? (($dashboardStats['subscriptions']['total'] - $dashboardStats['subscriptions']['auto_renew']) / $dashboardStats['subscriptions']['total']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expiry Status -->
            <div class="col-xl-6 col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Expiry Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="simple-chart pt-4 pb-2">
                            <div class="chart-item mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Expires Soon</span>
                                    <span class="chart-value text-warning font-weight-bold">{{ $dashboardStats['subscriptions']['expires_soon'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-warning" style="width: {{ $dashboardStats['subscriptions']['active'] > 0 ? ($dashboardStats['subscriptions']['expires_soon'] / $dashboardStats['subscriptions']['active']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="chart-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="chart-label">Safe</span>
                                    <span class="chart-value text-success font-weight-bold">{{ $dashboardStats['subscriptions']['active'] - $dashboardStats['subscriptions']['expires_soon'] }}</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" style="width: {{ $dashboardStats['subscriptions']['active'] > 0 ? (($dashboardStats['subscriptions']['active'] - $dashboardStats['subscriptions']['expires_soon']) / $dashboardStats['subscriptions']['active']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
<style>
    div.dataTables_wrapper div.dataTables_paginate{
        display: none;
    }
    .zoom {
        transition: transform .2s; /* Animation */
    }
    .zoom:hover {
        transform: scale(3.2);
    }
    /* Simple CSS Chart Styles */
    .simple-chart {
        padding: 1rem 0;
    }

    .chart-item {
        margin-bottom: 1rem;
    }

    .chart-label {
        font-size: 0.9rem;
        color: #5a5c69;
        font-weight: 500;
    }

    .chart-value {
        font-size: 0.9rem;
    }

    .progress {
        border-radius: 10px;
        background-color: #f8f9fc;
    }

    .progress-bar {
        border-radius: 10px;
        transition: width 0.6s ease;
    }
    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 0.25rem solid #e74a3b !important;
    }
    .card-hover {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
    .text-decoration-none:hover {
        text-decoration: none !important;
    }
    .card-hover:hover .text-gray-800 {
        color: #5a5c69 !important;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
<!-- No external chart library needed - Pure CSS charts for maximum speed -->

<!-- Page level custom scripts -->
<script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
<script>
    $('#dataTable').DataTable({
        "columnDefs": [
            {
                "orderable": false,
                "targets": [5]
            }
        ]
    });

    // No JavaScript needed - Pure CSS charts load instantly!
    $(document).ready(function() {
        // Charts are already rendered with CSS - no loading time needed
        console.log('Dashboard loaded with instant CSS charts!');
    });


</script>
@endpush
