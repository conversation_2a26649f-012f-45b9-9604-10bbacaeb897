@extends('backend.layouts.master')

@section('title','DynamoDB Dashboard')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">DynamoDB Tables Overview</h6>
    </div>
    <div class="card-body">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Tables</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ count($tablesData) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-database fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Items</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalItems) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Tables</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ collect($tablesData)->where('status', 'active')->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">AWS Region</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ env('AWS_DEFAULT_REGION', 'us-east-1') }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-globe fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tables List -->
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>S.N.</th>
                        <th>Table Name</th>
                        <th>Display Name</th>
                        <th>Item Count</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($tablesData as $index => $table)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>
                            <code>{{ $table['name'] }}</code>
                        </td>
                        <td>
                            <strong>{{ $table['display_name'] }}</strong>
                        </td>
                        <td>
                            <span class="badge badge-info">{{ number_format($table['count']) }}</span>
                        </td>
                        <td>
                            @if($table['status'] == 'active')
                                <span class="badge badge-success">Active</span>
                            @else
                                <span class="badge badge-danger">Error</span>
                            @endif
                        </td>
                        <td>
                            @php
                                $routeName = '';
                                switch($table['name']) {
                                    case 'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.app-notifications';
                                        break;
                                    case 'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.certificates';
                                        break;
                                    case 'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.certificate-recipients';
                                        break;
                                    case 'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.delegates';
                                        break;
                                    case 'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.recipients';
                                        break;
                                    case 'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.subscriptions';
                                        break;
                                    case 'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE':
                                        $routeName = 'dynamodb.users';
                                        break;
                                }
                            @endphp
                            
                            @if($routeName)
                                <a href="{{ route($routeName) }}" class="btn btn-primary btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="view" data-placement="bottom">
                                    <i class="fas fa-eye"></i>
                                </a>
                            @endif
                            
                            <a href="{{ route('dynamodb.table.show', $table['name']) }}" class="btn btn-info btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="raw data" data-placement="bottom">
                                <i class="fas fa-table"></i>
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
<style>
    div.dataTables_wrapper div.dataTables_paginate{
        display: none;
    }
    .zoom {
        transition: transform .2s; /* Animation */
    }
    .zoom:hover {
        transform: scale(3.2);
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>

<!-- Page level custom scripts -->
<script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
<script>
    $('#dataTable').DataTable({
        "columnDefs": [
            {
                "orderable": false,
                "targets": [5]
            }
        ]
    });
</script>
@endpush
