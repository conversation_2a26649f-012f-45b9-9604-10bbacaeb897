@extends('backend.layouts.master')

@section('main-content')
<div class="card">
    <h5 class="card-header">Environment Details</h5>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Name:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->name }}
                        @if($environment->is_default)
                            <span class="badge badge-info ml-2">Default</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Slug:</strong>
                    </div>
                    <div class="col-md-8">
                        <code>{{ $environment->slug }}</code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Status:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge {{ $environment->status_badge }}">
                            {{ $environment->status_text }}
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Description:</strong>
                    </div>
                    <div class="col-md-8">
                        {!! $environment->description ?? '<em class="text-muted">No description provided</em>' !!}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Created Date:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->created_at->format('M d, Y H:i A') }}
                        <small class="text-muted">({{ $environment->created_at->diffForHumans() }})</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Last Updated:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->updated_at->format('M d, Y H:i A') }}
                        <small class="text-muted">({{ $environment->updated_at->diffForHumans() }})</small>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card border-left-primary">
                    <div class="card-body">
                        <h6 class="card-title text-primary">Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('environments.edit', $environment->id) }}" class="btn btn-primary btn-sm mb-2">
                                <i class="fas fa-edit"></i> Edit Environment
                            </a>
                            
                            @if(!$environment->is_default)
                            <button class="btn btn-info btn-sm mb-2 set-default-btn" 
                                    data-id="{{ $environment->id }}" 
                                    data-name="{{ $environment->name }}">
                                <i class="fas fa-star"></i> Set as Default
                            </button>
                            @endif

                            <button class="btn btn-sm mb-2 toggle-status-btn {{ $environment->is_active ? 'btn-success' : 'btn-secondary' }}" 
                                    data-id="{{ $environment->id }}" 
                                    data-status="{{ $environment->is_active }}">
                                <i class="fas {{ $environment->is_active ? 'fa-toggle-on' : 'fa-toggle-off' }}"></i> 
                                {{ $environment->is_active ? 'Deactivate' : 'Activate' }}
                            </button>

                            <a href="{{ route('environments.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if($environment->config)
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Configuration</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>{{ json_encode($environment->config, JSON_PRETTY_PRINT) }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Environment Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Environment Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-primary">
                                        {{ $environment->isProduction() ? 'PROD' : 'DEV' }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Type</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold {{ $environment->is_active ? 'text-success' : 'text-danger' }}">
                                        {{ $environment->is_active ? 'ACTIVE' : 'INACTIVE' }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Status</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold {{ $environment->is_default ? 'text-info' : 'text-muted' }}">
                                        {{ $environment->is_default ? 'YES' : 'NO' }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Default</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-secondary">
                                        {{ $environment->config ? count($environment->config) : 0 }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Config Items</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .d-grid {
        display: grid;
    }
    .gap-2 {
        gap: 0.5rem;
    }
    pre code {
        font-size: 0.875rem;
        color: #333;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
<script>
    // Toggle status
    $('.toggle-status-btn').click(function() {
        var envId = $(this).data('id');
        var currentStatus = $(this).data('status');
        var btn = $(this);
        
        $.ajax({
            url: '/admin/environments/' + envId + '/toggle-status',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to toggle environment status');
            }
        });
    });

    // Set as default
    $('.set-default-btn').click(function() {
        var envId = $(this).data('id');
        var envName = $(this).data('name');
        
        swal({
            title: "Set as Default?",
            text: "Are you sure you want to set '" + envName + "' as the default environment?",
            icon: "info",
            buttons: true,
        })
        .then((willSet) => {
            if (willSet) {
                $.ajax({
                    url: '/admin/environments/' + envId + '/set-default',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Failed to set default environment');
                    }
                });
            }
        });
    });
</script>
@endpush
