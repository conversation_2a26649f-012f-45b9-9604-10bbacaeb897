@extends('backend.layouts.master')

@section('main-content')
<div class="card">
    <h5 class="card-header">Environment Details</h5>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>ID:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->id }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Environment:</strong>
                    </div>
                    <div class="col-md-8">
                        <code>{{ $environment->env }}</code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Type:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge {{ $environment->status_badge }}">
                            {{ $environment->status_text }}
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Created Date:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->created_at->format('M d, Y H:i A') }}
                        <small class="text-muted">({{ $environment->created_at->diffForHumans() }})</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Last Updated:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $environment->updated_at->format('M d, Y H:i A') }}
                        <small class="text-muted">({{ $environment->updated_at->diffForHumans() }})</small>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card border-left-primary">
                    <div class="card-body">
                        <h6 class="card-title text-primary">Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('environments.edit', $environment->id) }}" class="btn btn-primary btn-sm mb-2">
                                <i class="fas fa-edit"></i> Edit Environment
                            </a>

                            <a href="{{ route('environments.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Environment Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Environment Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-primary">
                                        {{ strtoupper($environment->env) }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Environment</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold {{ $environment->isProduction() ? 'text-danger' : 'text-warning' }}">
                                        {{ $environment->status_text }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Type</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-info">
                                        {{ $environment->id }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">ID</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-secondary">
                                        {{ $environment->created_at->format('M Y') }}
                                    </div>
                                    <div class="text-xs font-weight-bold text-uppercase mb-1">Created</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .d-grid {
        display: grid;
    }
    .gap-2 {
        gap: 0.5rem;
    }
</style>
@endpush
