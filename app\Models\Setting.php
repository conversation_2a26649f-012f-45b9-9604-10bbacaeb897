<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get a setting value by key
     */
    public static function get($key, $default = null)
    {
        $cacheKey = "setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->where('is_active', true)->first();
            
            if (!$setting) {
                return $default;
            }

            return self::castValue($setting->value, $setting->type);
        });
    }

    /**
     * Set a setting value
     */
    public static function set($key, $value, $type = 'string', $description = null)
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
                'is_active' => true
            ]
        );

        // Clear cache
        Cache::forget("setting_{$key}");

        return $setting;
    }

    /**
     * Cast value to appropriate type
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Get current app mode
     */
    public static function getAppMode()
    {
        return self::get('app_mode', 'test');
    }

    /**
     * Set app mode
     */
    public static function setAppMode($mode)
    {
        return self::set('app_mode', $mode, 'string', 'Application mode - live or test');
    }

    /**
     * Check if app is in live mode
     */
    public static function isLiveMode()
    {
        return self::getAppMode() === 'live';
    }

    /**
     * Check if app is in test mode
     */
    public static function isTestMode()
    {
        return self::getAppMode() === 'test';
    }

    /**
     * Toggle app mode
     */
    public static function toggleAppMode()
    {
        $currentMode = self::getAppMode();
        $newMode = $currentMode === 'live' ? 'test' : 'live';
        
        return self::setAppMode($newMode);
    }
}
