@extends('backend.layouts.master')

@section('title', $displayName . ' - Raw Data')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">{{ $displayName }} - Raw Data ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Raw DynamoDB Data</h6>
            <p>This view shows the raw data structure from DynamoDB table: <code>{{ $tableName }}</code></p>
        </div>

        <div class="table-responsive">
            @if(count($items) > 0)
                <table class="table table-bordered" id="raw-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Raw JSON Data</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($items as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <div style="max-height: 200px; overflow-y: auto;">
                                    <pre style="font-size: 11px; margin: 0;"><code>{{ json_encode($item, JSON_PRETTY_PRINT) }}</code></pre>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm" onclick="showFullData({{ $index }})" data-toggle="tooltip" title="View Full Data">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> No data found!</h6>
                    <p>There are no items in this DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Full Data Modal -->
<div class="modal fade" id="fullDataModal" tabindex="-1" role="dialog" aria-labelledby="fullDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullDataModalLabel">Full Item Data</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="fullDataContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="copyToClipboard()">Copy JSON</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: top;
    }
    pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.25rem;
        padding: 0.5rem;
        font-size: 11px;
        max-height: 300px;
        overflow-y: auto;
    }
    code {
        color: #495057;
    }
</style>
@endpush

@push('scripts')
<!-- Page level plugins -->
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    // Store the items data for modal display
    const itemsData = @json($items);

    $(document).ready(function() {
        $('#raw-dataTable').DataTable({
            "pageLength": 10,
            "order": [[ 0, "asc" ]],
            "columnDefs": [
                {
                    "targets": [1, 2], // JSON and Actions columns
                    "orderable": false
                }
            ]
        });
    });

    function showFullData(index) {
        const item = itemsData[index];
        const jsonString = JSON.stringify(item, null, 2);
        
        const content = `
            <div class="row">
                <div class="col-md-12">
                    <h6>Item Index: ${index + 1}</h6>
                    <pre><code id="jsonData">${jsonString}</code></pre>
                </div>
            </div>
        `;
        
        $('#fullDataContent').html(content);
        $('#fullDataModal').modal('show');
    }

    function copyToClipboard() {
        const jsonData = document.getElementById('jsonData').textContent;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(jsonData).then(function() {
                alert('JSON data copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
                fallbackCopyTextToClipboard(jsonData);
            });
        } else {
            fallbackCopyTextToClipboard(jsonData);
        }
    }

    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        
        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            const msg = successful ? 'successful' : 'unsuccessful';
            console.log('Fallback: Copying text command was ' + msg);
            if (successful) {
                alert('JSON data copied to clipboard!');
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }

        document.body.removeChild(textArea);
    }
</script>
@endpush
