@extends('backend.layouts.master')

@section('title', $displayName . ' - Raw Data')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">{{ $displayName }} - Raw Data ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Raw DynamoDB Data</h6>
            <p>This view shows the raw data structure from DynamoDB table: <code>{{ $tableName }}</code></p>
        </div>
        
        <div class="table-responsive">
            @if(count($items) > 0)
                <table class="table table-bordered table-striped" id="raw-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Raw JSON Data</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($items as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <div class="json-container">
                                    <pre class="json-data">{{ json_encode($item, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> No data found!</h6>
                    <p>There are no items in the DynamoDB table <code>{{ $tableName }}</code>.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .json-container {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 0.5rem;
    }
    
    .json-data {
        font-size: 11px;
        font-family: 'Courier New', monospace;
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
        background: transparent;
        border: none;
        color: #495057;
    }
    
    .table td {
        vertical-align: top;
    }
    
    .table td:first-child {
        width: 80px;
        text-align: center;
        vertical-align: middle;
    }
    
    .table td:last-child {
        width: auto;
    }
    
    /* JSON syntax highlighting */
    .json-data {
        color: #333;
    }
    
    .json-container::-webkit-scrollbar {
        width: 6px;
    }
    
    .json-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    .json-container::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    
    .json-container::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#raw-dataTable').DataTable({
            "pageLength": 10,
            "order": [[ 0, "asc" ]],
            "columnDefs": [
                {
                    "targets": [1], // JSON column
                    "orderable": false
                }
            ],
            "scrollX": true
        });
    });
</script>
@endpush
