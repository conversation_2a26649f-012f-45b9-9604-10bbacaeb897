<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::get('get-countries','ApiController@getCountries')
->name('get-countries');
Route::get('get-states','ApiController@getStates')
->name('get-states');
Route::get('get-states-by-country/{countryId}','ApiController@getStatesByCountry')
->name('get-states-by-country');
Route::get('get-plans','ApiController@getPlans')
->name('get-plans');
Route::get('get-subscribers','ApiController@getSubscribers')
->name('get-subscribers');
Route::get('get-subscriber/{id}','ApiController@getSubscriber')
->name('get-subscriber');

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});
