@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Edit Environment</h5>
    <div class="card-body">
        <form method="post" action="{{ route('environments.update', $environment->id) }}">
            @csrf 
            @method('PATCH')
            
            <div class="form-group">
                <label for="inputEnv" class="col-form-label">Environment <span class="text-danger">*</span></label>
                <input id="inputEnv" type="text" name="env" placeholder="Enter environment name (e.g., prod, dev, staging)" value="{{ old('env', $environment->env) }}" class="form-control">
                @error('env')
                <span class="text-danger">{{ $message }}</span>
                @enderror
                <small class="form-text text-muted">Enter a short environment identifier (e.g., prod, dev, staging, test)</small>
            </div>

            <div class="form-group">
                <label class="col-form-label">Environment Info</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="small text-muted">ID</label>
                            <input type="text" class="form-control-plaintext" readonly value="{{ $environment->id }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="small text-muted">Created Date</label>
                            <input type="text" class="form-control-plaintext" readonly value="{{ $environment->created_at->format('M d, Y H:i A') }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button class="btn btn-success" type="submit">Update</button>
                <a href="{{ route('environments.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<style>
    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    .form-control-plaintext {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Auto-convert to lowercase
        $('#inputEnv').on('input', function() {
            $(this).val($(this).val().toLowerCase());
        });
    });
</script>
@endpush
