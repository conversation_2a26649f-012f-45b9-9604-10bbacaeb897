@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Edit Environment</h5>
    <div class="card-body">
        <form method="post" action="{{ route('environments.update', $environment->id) }}">
            @csrf 
            @method('PATCH')
            
            <div class="form-group">
                <label for="inputTitle" class="col-form-label">Name <span class="text-danger">*</span></label>
                <input id="inputTitle" type="text" name="name" placeholder="Enter environment name" value="{{ old('name', $environment->name) }}" class="form-control">
                @error('name')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="description" class="col-form-label">Description</label>
                <textarea class="form-control" id="description" name="description" placeholder="Enter environment description">{{ old('description', $environment->description) }}</textarea>
                @error('description')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="config" class="col-form-label">Configuration (JSON)</label>
                <textarea class="form-control" id="config" name="config" rows="6" placeholder='{"debug": true, "log_level": "debug", "cache_enabled": false}'>{{ old('config', $environment->config ? json_encode($environment->config, JSON_PRETTY_PRINT) : '') }}</textarea>
                @error('config')
                <span class="text-danger">{{ $message }}</span>
                @enderror
                <small class="form-text text-muted">Enter configuration as valid JSON format</small>
            </div>

            <div class="form-group">
                <label class="col-form-label">Status & Settings</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="1" id="is_active" name="is_active" {{ old('is_active', $environment->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                        Active
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="1" id="is_default" name="is_default" {{ old('is_default', $environment->is_default) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_default">
                        Set as Default Environment
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label class="col-form-label">Environment Info</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="small text-muted">Slug</label>
                            <input type="text" class="form-control-plaintext" readonly value="{{ $environment->slug }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="small text-muted">Created Date</label>
                            <input type="text" class="form-control-plaintext" readonly value="{{ $environment->created_at->format('M d, Y H:i A') }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button class="btn btn-success" type="submit">Update</button>
                <a href="{{ route('environments.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/summernote/summernote.min.css') }}">
<style>
    .form-check {
        margin-bottom: 10px;
    }
    .form-check-label {
        margin-left: 5px;
    }
    .form-control-plaintext {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
    }
</style>
@endpush

@push('scripts')
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{ asset('backend/summernote/summernote.min.js') }}"></script>
<script>
    $('#lfm').filemanager('image');

    $(document).ready(function() {
        $('#description').summernote({
            placeholder: "Write short description.....",
            tabsize: 2,
            height: 120,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'help']]
            ]
        });

        // JSON validation for config field
        $('#config').on('blur', function() {
            var configValue = $(this).val().trim();
            if (configValue && configValue !== '') {
                try {
                    JSON.parse(configValue);
                    $(this).removeClass('is-invalid').addClass('is-valid');
                    $('.config-error').remove();
                } catch (e) {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                    if (!$('.config-error').length) {
                        $(this).after('<div class="config-error text-danger small mt-1">Invalid JSON format</div>');
                    }
                }
            } else {
                $(this).removeClass('is-invalid is-valid');
                $('.config-error').remove();
            }
        });

        // Auto-uncheck other default if this is checked
        $('#is_default').change(function() {
            if ($(this).is(':checked') && !{{ $environment->is_default ? 'true' : 'false' }}) {
                swal({
                    title: "Set as Default?",
                    text: "This will make this environment the default one. Continue?",
                    icon: "info",
                    buttons: true,
                })
                .then((willSet) => {
                    if (!willSet) {
                        $(this).prop('checked', false);
                    }
                });
            }
        });
    });
</script>
@endpush
