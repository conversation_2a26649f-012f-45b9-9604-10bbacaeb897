<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DynamoDBService;

class DynamoDBManagementController extends Controller
{
    protected $dynamoDBService;

    // Your actual DynamoDB table names
    protected $tableNames = [
        'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE'
    ];

    public function __construct(DynamoDBService $dynamoDBService)
    {
        $this->dynamoDBService = $dynamoDBService;
    }

    /**
     * DynamoDB Dashboard - Overview of all tables
     */
    public function dashboard()
    {
        $tablesData = [];
        $totalItems = 0;

        foreach ($this->tableNames as $tableName) {
            $result = $this->dynamoDBService->scanTable($tableName, 1000);
            $itemCount = $result['success'] ? $result['count'] : 0;
            $totalItems += $itemCount;

            $tablesData[] = [
                'name' => $tableName,
                'display_name' => $this->getDisplayName($tableName),
                'count' => $itemCount,
                'status' => $result['success'] ? 'active' : 'error'
            ];
        }

        return view('backend.dynamodb.dashboard', compact('tablesData', 'totalItems'));
    }

    /**
     * Show specific table data
     */
    public function showTable($tableName, Request $request)
    {
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $displayName = $this->getDisplayName($tableName);
        $items = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.table', compact('tableName', 'displayName', 'items', 'count'));
    }

    /**
     * App Notifications Management
     */
    public function appNotifications(Request $request)
    {
        $tableName = 'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $notifications = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.app-notifications', compact('notifications', 'count'));
    }

    /**
     * Certificates Management
     */
    public function certificates(Request $request)
    {
        $tableName = 'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $certificates = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.certificates', compact('certificates', 'count'));
    }

    /**
     * Certificate Recipients Management
     */
    public function certificateRecipients(Request $request)
    {
        $tableName = 'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $recipients = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.certificate-recipients', compact('recipients', 'count'));
    }

    /**
     * Delegates Management
     */
    public function delegates(Request $request)
    {
        $tableName = 'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $delegates = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.delegates', compact('delegates', 'count'));
    }

    /**
     * Recipients Management
     */
    public function recipients(Request $request)
    {
        $tableName = 'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $recipients = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.recipients', compact('recipients', 'count'));
    }

    /**
     * Subscriptions Management
     */
    public function subscriptions(Request $request)
    {
        $tableName = 'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $subscriptions = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.subscriptions', compact('subscriptions', 'count'));
    }

    /**
     * Users Management
     */
    public function users(Request $request)
    {
        $tableName = 'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $users = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.users', compact('users', 'count'));
    }

    /**
     * Get display name for table
     */
    private function getDisplayName($tableName)
    {
        $displayNames = [
            'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'App Notifications',
            'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Certificates',
            'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Certificate Recipients',
            'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Delegates',
            'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Recipients',
            'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Subscriptions',
            'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Users'
        ];

        return $displayNames[$tableName] ?? $tableName;
    }

    /**
     * Get item details
     */
    public function getItem(Request $request, $tableName)
    {
        $key = $request->get('key');

        if (!$key) {
            return response()->json([
                'success' => false,
                'message' => 'Key parameter is required'
            ], 400);
        }

        $result = $this->dynamoDBService->getItem($tableName, $key);

        return response()->json([
            'success' => $result['success'],
            'data' => $result['data'],
            'message' => $result['message']
        ]);
    }
}
