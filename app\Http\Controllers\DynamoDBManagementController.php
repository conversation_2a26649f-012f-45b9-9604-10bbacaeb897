<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DynamoDBService;
use App\Helpers\DynamoDBHelper;

class DynamoDBManagementController extends Controller
{
    protected $dynamoDBService;

    // Your actual DynamoDB table names
    protected $tableNames = [
        'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE',
        'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE'
    ];

    public function __construct(DynamoDBService $dynamoDBService)
    {
        $this->dynamoDBService = $dynamoDBService;
    }

    /**
     * DynamoDB Dashboard - Overview of all tables with comprehensive analytics
     */
    public function dashboard()
    {
        $tablesData = [];
        $totalItems = 0;
        $analytics = [];

        foreach ($this->tableNames as $tableName) {
            $result = $this->dynamoDBService->scanTable($tableName, 1000);
            $itemCount = $result['success'] ? $result['count'] : 0;
            $totalItems += $itemCount;

            $tablesData[] = [
                'name' => $tableName,
                'display_name' => $this->getDisplayName($tableName),
                'count' => $itemCount,
                'status' => $result['success'] ? 'active' : 'error'
            ];

            // Store analytics data for each table
            $analytics[$this->getDisplayName($tableName)] = [
                'count' => $itemCount,
                'data' => $result['success'] ? $result['data'] : []
            ];
        }

        // Calculate comprehensive analytics
        $dashboardStats = $this->calculateDashboardAnalytics($analytics);

        return view('backend.dynamodb.dashboard', compact('tablesData', 'totalItems', 'dashboardStats'));
    }

    /**
     * Calculate comprehensive dashboard analytics
     */
    private function calculateDashboardAnalytics($analytics)
    {
        $stats = [
            'aws_region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'app_notifications_count' => $analytics['App Notifications']['count'] ?? 0,
            'certificates_count' => $analytics['Certificates']['count'] ?? 0,
            'certificate_recipients_count' => $analytics['Certificate Recipients']['count'] ?? 0,
            'delegates_count' => $analytics['Delegates']['count'] ?? 0,
            'recipients_count' => $analytics['Recipients']['count'] ?? 0,
            'users_count' => $analytics['Users']['count'] ?? 0,
            'subscriptions' => [
                'total' => $analytics['Subscriptions']['count'] ?? 0,
                'active' => 0,
                'inactive' => 0,
                'auto_renew' => 0,
                'expires_soon' => 0,
                'monthly' => 0,
                'yearly' => 0
            ]
        ];

        // Analyze subscription data
        if (isset($analytics['Subscriptions']['data']) && !empty($analytics['Subscriptions']['data'])) {
            $subscriptions = $analytics['Subscriptions']['data'];

            foreach ($subscriptions as $subscription) {
                // Active/Inactive count
                if (isset($subscription['isActive'])) {
                    if ($subscription['isActive'] === true || $subscription['isActive'] === 'true') {
                        $stats['subscriptions']['active']++;
                    } else {
                        $stats['subscriptions']['inactive']++;
                    }
                }

                // Auto-renew count
                if (isset($subscription['autoRenewing']) &&
                    ($subscription['autoRenewing'] === true || $subscription['autoRenewing'] === 'true')) {
                    $stats['subscriptions']['auto_renew']++;
                }

                // Duration-based categorization
                if (isset($subscription['duration'])) {
                    $duration = (int)$subscription['duration'];
                    if ($duration <= 31) { // Monthly (up to 31 days)
                        $stats['subscriptions']['monthly']++;
                    } elseif ($duration >= 365) { // Yearly (365+ days)
                        $stats['subscriptions']['yearly']++;
                    }
                }

                // Check for expiring subscriptions (within 30 days)
                if (isset($subscription['endDate'])) {
                    $endDate = $this->parseTimestamp($subscription['endDate']);
                    if ($endDate && $endDate <= now()->addDays(30)) {
                        $stats['subscriptions']['expires_soon']++;
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * Parse timestamp from various formats
     */
    private function parseTimestamp($timestamp)
    {
        try {
            if (is_numeric($timestamp)) {
                // Unix timestamp
                return \Carbon\Carbon::createFromTimestamp($timestamp);
            } elseif (is_string($timestamp)) {
                // ISO string or other format
                return \Carbon\Carbon::parse($timestamp);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to parse timestamp: ' . $timestamp);
        }

        return null;
    }

    /**
     * Show specific table data
     */
    public function showTable($tableName, Request $request)
    {
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $displayName = $this->getDisplayName($tableName);
        $items = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.table', compact('tableName', 'displayName', 'items', 'count'));
    }

    /**
     * App Notifications Management
     */
    public function appNotifications(Request $request)
    {
        $tableName = 'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $notifications = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.app-notifications', compact('notifications', 'count'));
    }

    /**
     * Certificates Management
     */
    public function certificates(Request $request)
    {
        $tableName = 'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
        $limit = $request->get('limit', 100);
        $result = $this->dynamoDBService->scanTable($tableName, $limit);

        $certificates = $result['success'] ? $result['data'] : [];
        $count = $result['success'] ? $result['count'] : 0;

        return view('backend.dynamodb.certificates', compact('certificates', 'count'));
    }

    /**
     * Certificate Recipients Management
     */
    public function certificateRecipients(Request $request)
    {
        // For now, let's test with dummy data to see if the view works
        $recipients = [];
        $count = 0;

        try {
            $tableName = 'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
            $limit = $request->get('limit', 100);
            $result = $this->dynamoDBService->scanTable($tableName, $limit);

            $recipients = $result['success'] ? $result['data'] : [];
            $count = $result['success'] ? $result['count'] : 0;

            // Debug information
            if (!$result['success']) {
                \Log::error('DynamoDB Certificate Recipients Error: ' . $result['message']);
            }
        } catch (\Exception $e) {
            \Log::error('Certificate Recipients Controller Error: ' . $e->getMessage());
            // Continue with empty data
        }

        return view('backend.dynamodb.certificate-recipients', compact('recipients', 'count'));
    }

    /**
     * Delegates Management
     */
    public function delegates(Request $request)
    {
        try {
            $tableName = 'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
            $limit = $request->get('limit', 100);
            $result = $this->dynamoDBService->scanTable($tableName, $limit);

            $delegates = $result['success'] ? $result['data'] : [];
            $count = $result['success'] ? $result['count'] : 0;

            return view('backend.dynamodb.delegates', compact('delegates', 'count'));
        } catch (\Exception $e) {
            \Log::error('Delegates Controller Error: ' . $e->getMessage());
            return view('backend.dynamodb.delegates', ['delegates' => [], 'count' => 0]);
        }
    }

    /**
     * Recipients Management
     */
    public function recipients(Request $request)
    {
        try {
            $tableName = 'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
            $limit = $request->get('limit', 100);
            $result = $this->dynamoDBService->scanTable($tableName, $limit);

            $recipients = $result['success'] ? $result['data'] : [];
            $count = $result['success'] ? $result['count'] : 0;

            return view('backend.dynamodb.recipients', compact('recipients', 'count'));
        } catch (\Exception $e) {
            \Log::error('Recipients Controller Error: ' . $e->getMessage());
            return view('backend.dynamodb.recipients', ['recipients' => [], 'count' => 0]);
        }
    }

    /**
     * Subscriptions Management
     */
    public function subscriptions(Request $request)
    {
        try {
            $tableName = 'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
            $limit = $request->get('limit', 100);
            $result = $this->dynamoDBService->scanTable($tableName, $limit);

            $subscriptions = $result['success'] ? $result['data'] : [];
            $count = $result['success'] ? $result['count'] : 0;

            return view('backend.dynamodb.subscriptions', compact('subscriptions', 'count'));
        } catch (\Exception $e) {
            \Log::error('Subscriptions Controller Error: ' . $e->getMessage());
            return view('backend.dynamodb.subscriptions', ['subscriptions' => [], 'count' => 0]);
        }
    }

    /**
     * Users Management
     */
    public function users(Request $request)
    {
        try {
            $tableName = 'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE';
            $limit = $request->get('limit', 100);
            $result = $this->dynamoDBService->scanTable($tableName, $limit);

            $users = $result['success'] ? $result['data'] : [];
            $count = $result['success'] ? $result['count'] : 0;

            return view('backend.dynamodb.users', compact('users', 'count'));
        } catch (\Exception $e) {
            \Log::error('Users Controller Error: ' . $e->getMessage());
            return view('backend.dynamodb.users', ['users' => [], 'count' => 0]);
        }
    }

    /**
     * Get display name for table
     */
    private function getDisplayName($tableName)
    {
        $displayNames = [
            'AppNotification-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'App Notifications',
            'Certificate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Certificates',
            'CertificateRecipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Certificate Recipients',
            'Delegate-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Delegates',
            'Recipient-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Recipients',
            'Subs-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Subscriptions',
            'User-2ud6fn3ymvcgpgz474vs5tu4q4-NONE' => 'Users'
        ];

        return $displayNames[$tableName] ?? $tableName;
    }

    /**
     * Get item details
     */
    public function getItem(Request $request, $tableName)
    {
        $key = $request->get('key');

        if (!$key) {
            return response()->json([
                'success' => false,
                'message' => 'Key parameter is required'
            ], 400);
        }

        $result = $this->dynamoDBService->getItem($tableName, $key);

        return response()->json([
            'success' => $result['success'],
            'data' => $result['data'],
            'message' => $result['message']
        ]);
    }

    /**
     * Format DynamoDB timestamp for display
     */
    public static function formatDynamoTimestamp($timestamp)
    {
        return DynamoDBHelper::formatTimestamp($timestamp);
    }
}
