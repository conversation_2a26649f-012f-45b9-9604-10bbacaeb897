@extends('backend.layouts.master')

@section('title','Certificate Recipients - DynamoDB')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Certificate Recipients ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($recipients) > 0)
                <table class="table table-bordered" id="recipient-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Certificate ID</th>
                            <th>Delivery Mode</th>
                            <th>Delivery Status</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recipients as $index => $recipient)
                        <tr class="clickable-row">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <code style="font-size: 10px;">{{ substr($recipient['id'] ?? 'N/A', 0, 20) }}...</code>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ $recipient['__typename'] ?? 'N/A' }}</span>
                            </td>
                            <td>
                                @if(isset($recipient['certificateId']))
                                    <code style="font-size: 10px;">{{ substr($recipient['certificateId'], 0, 20) }}...</code>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['deliveryMode']))
                                    <span class="badge badge-primary">{{ $recipient['deliveryMode'] }}</span>
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['deliveryStatus']))
                                    @switch($recipient['deliveryStatus'])
                                        @case('DELIVERED')
                                            <span class="badge badge-success">{{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @case('PENDING')
                                            <span class="badge badge-warning">{{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @case('QUEUED')
                                            <span class="badge badge-info">{{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $recipient['deliveryStatus'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($recipient['createdAt'] ?? null) }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No certificate recipients found!</h6>
                    <p>There are no certificate recipients in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#recipient-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [1, 3], // ID columns
                    "orderable": false
                }
            ]
        });
    });
</script>
@endpush
