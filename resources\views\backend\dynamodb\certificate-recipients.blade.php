@extends('backend.layouts.master')

@section('title','Certificate Recipients - DynamoDB')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Certificate Recipients ({{ $count }} items)</h6>
        <a href="{{ route('dynamodb.dashboard') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if(count($recipients) > 0)
                <table class="table table-bordered" id="recipient-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Owner</th>
                            <th>Delivery Mode</th>
                            <th>Delivery Status</th>
                            <th>Recipient ID</th>
                            <th>Send Date</th>
                            <th>Fax Send Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recipients as $index => $recipient)
                        <tr class="clickable-row" data-recipient-id="{{ $recipient['id'] ?? '' }}">
                            <td>{{ $index + 1 }}</td>
                            <td>
                                <strong>{{ $recipient['owner'] ?? 'N/A' }}</strong>
                                @if(isset($recipient['certificateId']))
                                    <br><small class="text-muted">Cert: <code>{{ substr($recipient['certificateId'], 0, 20) }}...</code></small>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['deliveryMode']))
                                    @switch($recipient['deliveryMode'])
                                        @case('EMAIL')
                                            <span class="badge badge-primary"><i class="fas fa-envelope"></i> {{ $recipient['deliveryMode'] }}</span>
                                            @break
                                        @case('FAX')
                                            <span class="badge badge-info"><i class="fas fa-fax"></i> {{ $recipient['deliveryMode'] }}</span>
                                            @break
                                        @case('SMS')
                                            <span class="badge badge-warning"><i class="fas fa-sms"></i> {{ $recipient['deliveryMode'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $recipient['deliveryMode'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['deliveryStatus']))
                                    @switch($recipient['deliveryStatus'])
                                        @case('DELIVERED')
                                            <span class="badge badge-success"><i class="fas fa-check"></i> {{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @case('PENDING')
                                            <span class="badge badge-warning"><i class="fas fa-clock"></i> {{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @case('QUEUED')
                                            <span class="badge badge-info"><i class="fas fa-hourglass-half"></i> {{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @case('FAILED')
                                            <span class="badge badge-danger"><i class="fas fa-times"></i> {{ $recipient['deliveryStatus'] }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $recipient['deliveryStatus'] }}</span>
                                    @endswitch
                                @else
                                    <span class="badge badge-secondary">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($recipient['recipientId']))
                                    <code style="font-size: 10px;">{{ substr($recipient['recipientId'], 0, 15) }}...</code>
                                    @if(isset($recipient['secondUserId']))
                                        <br><small class="text-muted">2nd: <code>{{ substr($recipient['secondUserId'], 0, 10) }}...</code></small>
                                    @endif
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($recipient['sendDate'] ?? null) }}
                            </td>
                            <td>
                                {{ App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($recipient['faxSendDate'] ?? null) }}
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-recipient='@json($recipient)'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                @if(isset($recipient['failureDetails']) && $recipient['failureDetails'])
                                    <button class="btn btn-danger btn-sm view-failure" data-toggle="tooltip" title="View Failure Details" data-failure="{{ $recipient['failureDetails'] }}">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                @endif
                                @if($recipient['deliveryStatus'] === 'DELIVERED')
                                    <span class="btn btn-success btn-sm" data-toggle="tooltip" title="Successfully Delivered">
                                        <i class="fas fa-check-circle"></i>
                                    </span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No certificate recipients found!</h6>
                    <p>There are no certificate recipients in the DynamoDB table.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Certificate Recipient Details Modal -->
<div class="modal fade" id="recipientModal" tabindex="-1" role="dialog" aria-labelledby="recipientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recipientModalLabel">Certificate Recipient Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="recipientDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Failure Details Modal -->
<div class="modal fade" id="failureModal" tabindex="-1" role="dialog" aria-labelledby="failureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="failureModalLabel">Delivery Failure Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="failureDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details, .view-failure {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
    .failure-details {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 1rem;
        color: #721c24;
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        $('#recipient-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 5, "desc" ]], // Order by send date
            "columnDefs": [
                {
                    "targets": [4, 7], // Recipient ID and Actions columns
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var recipient = $(this).data('recipient');
            showRecipientDetails(recipient);
        });

        // Handle view failure button click
        $('.view-failure').on('click', function() {
            var failure = $(this).data('failure');
            showFailureDetails(failure);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showRecipientDetails(recipient) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${recipient.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${recipient.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${recipient.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Certificate ID:</th>
                            <td><code>${recipient.certificateId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Recipient ID:</th>
                            <td><code>${recipient.recipientId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Second User ID:</th>
                            <td><code>${recipient.secondUserId || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Delivery Mode:</th>
                            <td>${getDeliveryModeBadge(recipient.deliveryMode)}</td>
                        </tr>
                        <tr>
                            <th>Delivery Status:</th>
                            <td>${getDeliveryStatusBadge(recipient.deliveryStatus)}</td>
                        </tr>
                        <tr>
                            <th>Send Date:</th>
                            <td>${recipient.sendDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Fax Send Date:</th>
                            <td>${recipient.faxSendDate || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${recipient.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${recipient.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Failure Details:</th>
                            <td>${recipient.failureDetails ? `<div class="failure-details">${recipient.failureDetails}</div>` : '<span class="text-success">No failures</span>'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#recipientDetails').html(detailsHtml);
        $('#recipientModal').modal('show');
    }

    function showFailureDetails(failure) {
        var failureHtml = `
            <div class="failure-details">
                <h6><i class="fas fa-exclamation-triangle"></i> Delivery Failure Information</h6>
                <p>${failure}</p>
            </div>
        `;

        $('#failureDetails').html(failureHtml);
        $('#failureModal').modal('show');
    }

    function getDeliveryModeBadge(mode) {
        if (!mode) return '<span class="badge badge-secondary">N/A</span>';

        switch(mode) {
            case 'EMAIL':
                return '<span class="badge badge-primary"><i class="fas fa-envelope"></i> ' + mode + '</span>';
            case 'FAX':
                return '<span class="badge badge-info"><i class="fas fa-fax"></i> ' + mode + '</span>';
            case 'SMS':
                return '<span class="badge badge-warning"><i class="fas fa-sms"></i> ' + mode + '</span>';
            default:
                return '<span class="badge badge-secondary">' + mode + '</span>';
        }
    }

    function getDeliveryStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'DELIVERED':
                return '<span class="badge badge-success"><i class="fas fa-check"></i> ' + status + '</span>';
            case 'PENDING':
                return '<span class="badge badge-warning"><i class="fas fa-clock"></i> ' + status + '</span>';
            case 'QUEUED':
                return '<span class="badge badge-info"><i class="fas fa-hourglass-half"></i> ' + status + '</span>';
            case 'FAILED':
                return '<span class="badge badge-danger"><i class="fas fa-times"></i> ' + status + '</span>';
            default:
                return '<span class="badge badge-secondary">' + status + '</span>';
        }
    }
</script>
@endpush
