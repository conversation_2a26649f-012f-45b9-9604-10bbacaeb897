<?php

namespace App\Helpers;

use Carbon\Carbon;
use Exception;

class DynamoDBHelper
{
    /**
     * Format DynamoDB timestamp for display
     * Handles various DynamoDB timestamp formats including microseconds
     */
    public static function formatTimestamp($timestamp)
    {
        if (!$timestamp || $timestamp === 'null' || $timestamp === null) {
            return 'N/A';
        }

        try {
            // Handle DynamoDB timestamp format: 2025-05-26T23:29:33.834000000Z
            $cleanTimestamp = $timestamp;
            
            // Remove the 'Z' and replace with timezone offset
            if (str_ends_with($cleanTimestamp, 'Z')) {
                $cleanTimestamp = str_replace('Z', '+00:00', $cleanTimestamp);
            }
            
            // Handle microseconds - DynamoDB sometimes has 9 digits after decimal
            // PHP can only handle 6 digits (microseconds), so we need to truncate
            if (preg_match('/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\.(\d+)(\+\d{2}:\d{2})/', $cleanTimestamp, $matches)) {
                $datePart = $matches[1];
                $microseconds = $matches[2];
                $timezone = $matches[3];
                
                // Truncate microseconds to 6 digits
                $microseconds = substr($microseconds, 0, 6);
                $microseconds = str_pad($microseconds, 6, '0');
                
                $cleanTimestamp = $datePart . '.' . $microseconds . $timezone;
            }

            // Try different formats in order of likelihood
            $formats = [
                'Y-m-d\TH:i:s.uP',      // 2025-05-26T23:29:33.834000+00:00
                'Y-m-d\TH:i:sP',        // 2025-05-26T23:29:33+00:00
                'Y-m-d\TH:i:s.u\Z',     // 2025-05-26T23:29:33.834000Z
                'Y-m-d\TH:i:s\Z',       // 2025-05-26T23:29:33Z
                'Y-m-d H:i:s.u',        // 2025-05-26 23:29:33.834000
                'Y-m-d H:i:s',          // 2025-05-26 23:29:33
                'Y-m-d\TH:i:s',         // 2025-05-26T23:29:33
            ];

            foreach ($formats as $format) {
                try {
                    $date = Carbon::createFromFormat($format, $cleanTimestamp);
                    if ($date !== false) {
                        return $date->format('Y-m-d H:i:s');
                    }
                } catch (Exception $e) {
                    continue;
                }
            }

            // If all specific formats fail, try Carbon's parse method
            $date = Carbon::parse($cleanTimestamp);
            return $date->format('Y-m-d H:i:s');
            
        } catch (Exception $e) {
            // If all parsing fails, return the original timestamp
            return $timestamp;
        }
    }

    /**
     * Format DynamoDB timestamp for display with timezone
     */
    public static function formatTimestampWithTimezone($timestamp, $timezone = 'UTC')
    {
        if (!$timestamp || $timestamp === 'null' || $timestamp === null) {
            return 'N/A';
        }

        try {
            $formattedTime = self::formatTimestamp($timestamp);
            if ($formattedTime === 'N/A' || $formattedTime === $timestamp) {
                return $formattedTime;
            }

            $date = Carbon::parse($formattedTime)->setTimezone($timezone);
            return $date->format('Y-m-d H:i:s T');
            
        } catch (Exception $e) {
            return self::formatTimestamp($timestamp);
        }
    }

    /**
     * Get human readable time difference
     */
    public static function getTimeAgo($timestamp)
    {
        if (!$timestamp || $timestamp === 'null' || $timestamp === null) {
            return 'N/A';
        }

        try {
            $formattedTime = self::formatTimestamp($timestamp);
            if ($formattedTime === 'N/A' || $formattedTime === $timestamp) {
                return $formattedTime;
            }

            $date = Carbon::parse($formattedTime);
            return $date->diffForHumans();
            
        } catch (Exception $e) {
            return self::formatTimestamp($timestamp);
        }
    }

    /**
     * Check if timestamp is valid
     */
    public static function isValidTimestamp($timestamp)
    {
        if (!$timestamp || $timestamp === 'null' || $timestamp === null) {
            return false;
        }

        try {
            $formattedTime = self::formatTimestamp($timestamp);
            return $formattedTime !== 'N/A' && $formattedTime !== $timestamp;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Format DynamoDB boolean values
     */
    public static function formatBoolean($value)
    {
        if ($value === true || $value === 'true' || $value === 1 || $value === '1') {
            return 'Yes';
        } elseif ($value === false || $value === 'false' || $value === 0 || $value === '0') {
            return 'No';
        } else {
            return 'N/A';
        }
    }

    /**
     * Format DynamoDB array/list values
     */
    public static function formatArray($value)
    {
        if (!is_array($value)) {
            return 'N/A';
        }

        if (empty($value)) {
            return 'Empty';
        }

        return count($value) . ' items';
    }

    /**
     * Truncate long text with ellipsis
     */
    public static function truncateText($text, $length = 50)
    {
        if (!$text || strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }

    /**
     * Format status badges
     */
    public static function getStatusBadge($status)
    {
        $statusClasses = [
            'ACTIVE' => 'badge-success',
            'INACTIVE' => 'badge-secondary',
            'PENDING' => 'badge-warning',
            'DELIVERED' => 'badge-success',
            'QUEUED' => 'badge-info',
            'FAILED' => 'badge-danger',
            'ERROR' => 'badge-danger',
            'SUCCESS' => 'badge-success',
            'CANCELLED' => 'badge-secondary',
            'EXPIRED' => 'badge-warning',
        ];

        $class = $statusClasses[strtoupper($status)] ?? 'badge-secondary';
        return "<span class=\"badge {$class}\">{$status}</span>";
    }
}
