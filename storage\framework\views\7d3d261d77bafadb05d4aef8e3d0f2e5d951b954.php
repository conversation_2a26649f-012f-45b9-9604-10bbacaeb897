<?php $__env->startSection('title','Certificates - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Certificates (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($certificates) > 0): ?>
                <table class="table table-bordered" id="certificate-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Archive Status</th>
                            <th>Certificate Status</th>
                            <th>Certificate Back</th>
                            <th>Certificate Front</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $certificates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <code style="font-size: 10px;"><?php echo e(substr($certificate['id'] ?? 'N/A', 0, 20)); ?>...</code>
                            </td>
                            <td>
                                <span class="badge badge-info"><?php echo e($certificate['__typename'] ?? 'N/A'); ?></span>
                            </td>
                            <td>
                                <?php if(isset($certificate['archiveStatus'])): ?>
                                    <?php if($certificate['archiveStatus'] == 'NotArchived'): ?>
                                        <span class="badge badge-success">Not Archived</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning"><?php echo e($certificate['archiveStatus']); ?></span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($certificate['certificateStatus'])): ?>
                                    <?php if($certificate['certificateStatus'] == 'ACTIVE'): ?>
                                        <span class="badge badge-success"><?php echo e($certificate['certificateStatus']); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-danger"><?php echo e($certificate['certificateStatus']); ?></span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($certificate['certBack']) && $certificate['certBack'] !== null): ?>
                                    <span class="badge badge-info">Available</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">null</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($certificate['certFront']) && $certificate['certFront'] !== null): ?>
                                    <span class="badge badge-info">Available</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">null</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No certificates found!</h6>
                    <p>There are no certificates in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Certificate Details Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1" role="dialog" aria-labelledby="certificateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="certificateModalLabel">Certificate Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="certificateDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Page level plugins -->
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#certificate-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 0, "asc" ]],
            "columnDefs": [
                {
                    "targets": [1], // ID column
                    "orderable": false
                }
            ]
        });

        // Click handler for viewing full certificate details
        $('tbody').on('click', '.clickable-row', function() {
            var data = $('#certificate-dataTable').DataTable().row(this).data();
            if (data) {
                showCertificateDetails(this);
            }
        });
    });

    function showCertificateDetails(row) {
        // Get the certificate data from the row
        var cells = $(row).find('td');
        var id = $(cells[1]).find('code').text();
        var type = $(cells[2]).find('.badge').text();
        var archiveStatus = $(cells[3]).find('.badge').text();
        var certificateStatus = $(cells[4]).find('.badge').text();
        var certBack = $(cells[5]).find('.badge').text();
        var certFront = $(cells[6]).find('.badge').text();

        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">ID:</th>
                            <td><code>${id}</code></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><span class="badge badge-info">${type}</span></td>
                        </tr>
                        <tr>
                            <th>Archive Status:</th>
                            <td>${archiveStatus}</td>
                        </tr>
                        <tr>
                            <th>Certificate Status:</th>
                            <td>${certificateStatus}</td>
                        </tr>
                        <tr>
                            <th>Certificate Back:</th>
                            <td>${certBack}</td>
                        </tr>
                        <tr>
                            <th>Certificate Front:</th>
                            <td>${certFront}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#certificateDetails').html(detailsHtml);
        $('#certificateModal').modal('show');
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/certificates.blade.php ENDPATH**/ ?>