<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    /**
     * Display settings page
     */
    public function index()
    {
        $settings = Setting::where('is_active', true)->orderBy('key')->get();
        
        return view('backend.settings.index', compact('settings'));
    }

    /**
     * Toggle app mode between live and test
     */
    public function toggleMode(Request $request)
    {
        try {
            $currentMode = Setting::getAppMode();
            $newMode = $currentMode === 'live' ? 'test' : 'live';
            
            // Update app mode
            Setting::setAppMode($newMode);
            
            // Also update database and AWS modes
            Setting::set('database_mode', $newMode, 'string', 'Database mode - live or test');
            Setting::set('aws_mode', $newMode, 'string', 'AWS DynamoDB mode - live or test');
            
            // Log the mode change
            \Log::info('App mode changed', [
                'user_id' => Auth::id(),
                'user_email' => Auth::user()->email,
                'from_mode' => $currentMode,
                'to_mode' => $newMode,
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Mode switched to {$newMode} successfully!",
                'current_mode' => $newMode,
                'is_live' => $newMode === 'live'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to toggle app mode', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle mode. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current app mode
     */
    public function getCurrentMode()
    {
        $mode = Setting::getAppMode();
        
        return response()->json([
            'current_mode' => $mode,
            'is_live' => $mode === 'live',
            'is_test' => $mode === 'test'
        ]);
    }

    /**
     * Update a specific setting
     */
    public function updateSetting(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
            'type' => 'required|in:string,boolean,integer,float,json'
        ]);

        try {
            $setting = Setting::set(
                $request->key,
                $request->value,
                $request->type,
                $request->description
            );

            return response()->json([
                'success' => true,
                'message' => 'Setting updated successfully!',
                'setting' => $setting
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update setting.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
