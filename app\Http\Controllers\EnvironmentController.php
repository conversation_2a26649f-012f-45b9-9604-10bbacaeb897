<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Environment;
use Illuminate\Http\Request;

class EnvironmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of environments
     */
    public function index()
    {
        $environments = Environment::orderBy('created_at', 'desc')->paginate(10);

        return view('backend.environments.index', compact('environments'));
    }

    /**
     * Show the form for creating a new environment
     */
    public function create()
    {
        return view('backend.environments.create');
    }

    /**
     * Store a newly created environment
     */
    public function store(Request $request)
    {
        $request->validate([
            'env' => 'required|string|max:255|unique:environments,env'
        ]);

        try {
            Environment::create([
                'env' => $request->env
            ]);

            return redirect()->route('environments.index')
                ->with('success', 'Environment created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create environment: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified environment
     */
    public function show(Environment $environment)
    {
        return view('backend.environments.show', compact('environment'));
    }

    /**
     * Show the form for editing the specified environment
     */
    public function edit(Environment $environment)
    {
        return view('backend.environments.edit', compact('environment'));
    }

    /**
     * Update the specified environment
     */
    public function update(Request $request, Environment $environment)
    {
        $request->validate([
            'env' => 'required|string|max:255|unique:environments,env,' . $environment->id
        ]);

        try {
            $environment->update([
                'env' => $request->env
            ]);

            return redirect()->route('environments.index')
                ->with('success', 'Environment updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update environment: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified environment
     */
    public function destroy(Environment $environment)
    {
        try {
            $environment->delete();

            return redirect()->route('environments.index')
                ->with('success', 'Environment deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete environment: ' . $e->getMessage());
        }
    }

    /**
     * Get current environment for toggle
     */
    public function getCurrentEnvironment()
    {
        try {
            // Get the first environment (assuming we're using the first record as current)
            $currentEnv = Environment::first();

            if (!$currentEnv) {
                // If no environment exists, create default dev environment
                $currentEnv = Environment::create(['env' => 'dev']);
            }

            return response()->json([
                'success' => true,
                'environment' => $currentEnv->env
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get current environment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update current environment
     */
    public function updateCurrentEnvironment(Request $request)
    {
        $request->validate([
            'environment' => 'required|in:prod,dev'
        ]);

        try {
            // Find the environment record to update
            $environment = Environment::where('env', $request->environment)->first();

            if (!$environment) {
                // If environment doesn't exist, create it
                $environment = Environment::create(['env' => $request->environment]);
            }

            // Update all environments to set the selected one as the first/current
            // We'll use a simple approach: delete all and recreate with the selected one first
            Environment::truncate();

            // Create the selected environment first
            Environment::create(['env' => $request->environment]);

            // Create the other environment
            $otherEnv = $request->environment === 'prod' ? 'dev' : 'prod';
            Environment::create(['env' => $otherEnv]);

            return response()->json([
                'success' => true,
                'message' => 'Environment updated successfully!',
                'environment' => $request->environment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update environment: ' . $e->getMessage()
            ], 500);
        }
    }
}
