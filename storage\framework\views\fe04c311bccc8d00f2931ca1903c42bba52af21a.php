<ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

    <!-- Sidebar - Brand -->
    <a class="sidebar-brand d-flex align-items-center justify-content-center" href="<?php echo e(route('admin')); ?>">
      <div class="sidebar-brand-icon rotate-n-15">
        <i class="fas fa-laugh-wink"></i>
      </div>
      <div class="sidebar-brand-text mx-3">Admin</div>
    </a>

    <!-- Divider -->
    <hr class="sidebar-divider my-0">



    <!-- Divider -->
    <hr class="sidebar-divider">

    <!-- DynamoDB Dashboard -->
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.dashboard')); ?>">
            <i class="fas fa-fw fa-tachometer-alt"></i>
            <span>AWS Dashboard</span></a>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider">

    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.app-notifications')); ?>">
                    <i class="fas fa-bell"></i> <span>App Notifications</span>
                </a>
    </li>
        <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.certificates')); ?>">
                    <i class="fas fa-certificate"></i> <span>Certificates</span>
                </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.certificate-recipients')); ?>">
                    <i class="fas fa-user-check"></i> <span>Certificate Recipients</span>
                </a>
    </li>

    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.delegates')); ?>">
                    <i class="fas fa-user-tie"></i> <span>Delegates</span>
                </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.recipients')); ?>">
                    <i class="fas fa-users"></i> <span>Recipients</span>
                </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.subscriptions')); ?>">
                    <i class="fas fa-credit-card"></i> <span>Subscriptions</span>
                </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('dynamodb.users')); ?>">
                    <i class="fas fa-user"></i> <span>DynamoDB Users</span>
                </a>
</li>



        <!-- Nav Item - Dashboard -->
    <li class="nav-item active">
      <a class="nav-link" href="<?php echo e(route('admin')); ?>">
        <i class="fas fa-fw fa-tachometer-alt"></i>
        <span>Dashboard</span></a>
    </li>


    <!-- Divider -->
    <hr class="sidebar-divider">
     <!-- Heading -->
    <div class="sidebar-heading">
        System Configuration
    </div>

    <!-- Environments -->
    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#environmentCollapse" aria-expanded="true" aria-controls="environmentCollapse">
            <i class="fas fa-cogs"></i>
            <span>Environments</span>
        </a>
        <div id="environmentCollapse" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Environment Options:</h6>
                <a class="collapse-item" href="<?php echo e(route('environments.index')); ?>">All Environments</a>
                <a class="collapse-item" href="<?php echo e(route('environments.create')); ?>">Add Environment</a>
            </div>
        </div>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider">
     <!-- Heading -->
    <div class="sidebar-heading">
        Master Data Management
    </div>

            <!-- Plans -->
    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#planCollapse" aria-expanded="true" aria-controls="planCollapse">
            <i class="fas fa-clipboard-list"></i>
            <span>Plans</span>
        </a>
        <div id="planCollapse" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Plan Options:</h6>
                <a class="collapse-item" href="<?php echo e(route('plans.index')); ?>">All Plans</a>
                <a class="collapse-item" href="<?php echo e(route('plans.create')); ?>">Add Plan</a>
            </div>
        </div>
    </li>

    <!-- Countries -->
    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#countryCollapse" aria-expanded="true" aria-controls="countryCollapse">
            <i class="fas fa-globe"></i>
            <span>Countries</span>
        </a>
        <div id="countryCollapse" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Country Options:</h6>
                <a class="collapse-item" href="<?php echo e(route('countries.index')); ?>">All Countries</a>
                <a class="collapse-item" href="<?php echo e(route('countries.create')); ?>">Add Country</a>
            </div>
        </div>
    </li>

    <!-- States -->
    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#stateCollapse" aria-expanded="true" aria-controls="stateCollapse">
            <i class="fas fa-map-marker-alt"></i>
            <span>States</span>
        </a>
        <div id="stateCollapse" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">State Options:</h6>
                <a class="collapse-item" href="<?php echo e(route('states.index')); ?>">All States</a>
                <a class="collapse-item" href="<?php echo e(route('states.create')); ?>">Add State</a>
            </div>
        </div>
    </li>

    <!-- Subscribers -->
    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#subscriberCollapse" aria-expanded="true" aria-controls="subscriberCollapse">
            <i class="fas fa-users"></i>
            <span>Subscribers</span>
        </a>
        <div id="subscriberCollapse" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Subscriber Options:</h6>
                <a class="collapse-item" href="<?php echo e(route('subscribers.index')); ?>">All Subscribers</a>
                <a class="collapse-item" href="<?php echo e(route('subscribers.create')); ?>">Add Subscriber</a>
            </div>
        </div>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider">
     <!-- Heading -->
    <div class="sidebar-heading">
        User Management
    </div>

     <!-- Users -->
     <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('users.index')); ?>">
            <i class="fas fa-users"></i>
            <span>System Users</span></a>
    </li>




    <!-- Sidebar Toggler (Sidebar) -->
    <div class="text-center d-none d-md-inline">
      <button class="rounded-circle border-0" id="sidebarToggle"></button>
    </div>

</ul><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/layouts/sidebar.blade.php ENDPATH**/ ?>