@extends('backend.layouts.master')

@section('main-content')
<!-- DataTales Example -->
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Application Settings</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Mode Settings Card -->
            <div class="col-lg-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Application Mode</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="mb-1">Current Mode: <span id="currentModeDisplay" class="font-weight-bold">Loading...</span></h5>
                                <p class="text-muted mb-0" id="currentModeDescription">Checking current mode...</p>
                            </div>
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="settingsModeToggle">
                                <label class="custom-control-label" for="settingsModeToggle"></label>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>Live Mode:</strong> Production environment with real data<br>
                            <strong>Test Mode:</strong> Development environment with test data
                        </div>
                        
                        <button class="btn btn-primary btn-sm" onclick="toggleModeFromSettings()">
                            <i class="fas fa-sync-alt"></i> Toggle Mode
                        </button>
                    </div>
                </div>
            </div>

            <!-- Database Settings Card -->
            <div class="col-lg-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-success">Database Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Database Mode:</strong></label>
                            <p id="dbModeDisplay" class="text-muted">Loading...</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label"><strong>AWS DynamoDB Mode:</strong></label>
                            <p id="awsModeDisplay" class="text-muted">Loading...</p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <small><i class="fas fa-info-circle"></i> Database and AWS modes are automatically synchronized with the application mode.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">All Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="settingsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Key</th>
                                        <th>Value</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Last Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($settings as $setting)
                                    <tr>
                                        <td><code>{{ $setting->key }}</code></td>
                                        <td>
                                            @if($setting->type === 'boolean')
                                                <span class="badge badge-{{ $setting->value === 'true' ? 'success' : 'secondary' }}">
                                                    {{ $setting->value === 'true' ? 'True' : 'False' }}
                                                </span>
                                            @else
                                                <span class="badge badge-info">{{ $setting->value }}</span>
                                            @endif
                                        </td>
                                        <td><small class="text-muted">{{ $setting->type }}</small></td>
                                        <td>{{ $setting->description ?? 'No description' }}</td>
                                        <td>
                                            <span class="badge badge-{{ $setting->is_active ? 'success' : 'danger' }}">
                                                {{ $setting->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td><small class="text-muted">{{ $setting->updated_at->diffForHumans() }}</small></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
<style>
    .custom-switch .custom-control-label::before {
        width: 3rem;
        height: 1.5rem;
        border-radius: 1rem;
    }
    .custom-switch .custom-control-label::after {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 50%;
        top: 0.125rem;
    }
    .custom-switch .custom-control-input:checked ~ .custom-control-label::after {
        transform: translateX(1.5rem);
    }
</style>
@endpush

@push('scripts')
<script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>

<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#settingsTable').DataTable({
            "order": [[ 0, "asc" ]]
        });

        // Load current settings
        loadSettingsMode();

        // Handle toggle change
        $('#settingsModeToggle').change(function() {
            toggleModeFromSettings();
        });
    });

    function loadSettingsMode() {
        $.ajax({
            url: '{{ route("settings.current-mode") }}',
            type: 'GET',
            success: function(response) {
                updateSettingsDisplay(response.current_mode, response.is_live);
            },
            error: function() {
                $('#currentModeDisplay').text('Error loading mode');
                $('#currentModeDescription').text('Failed to load current mode');
            }
        });
    }

    function updateSettingsDisplay(mode, isLive) {
        $('#settingsModeToggle').prop('checked', isLive);
        
        if (isLive) {
            $('#currentModeDisplay').text('LIVE').removeClass('text-warning').addClass('text-success');
            $('#currentModeDescription').text('Production mode is currently active');
            $('#dbModeDisplay').text('Live Database').addClass('text-success');
            $('#awsModeDisplay').text('Live AWS DynamoDB').addClass('text-success');
        } else {
            $('#currentModeDisplay').text('TEST').removeClass('text-success').addClass('text-warning');
            $('#currentModeDescription').text('Test mode is currently active');
            $('#dbModeDisplay').text('Test Database').addClass('text-warning');
            $('#awsModeDisplay').text('Test AWS DynamoDB').addClass('text-warning');
        }
    }

    function toggleModeFromSettings() {
        // Show loading state
        $('#currentModeDisplay').text('Switching...');
        $('#currentModeDescription').text('Please wait while we switch modes...');
        $('#settingsModeToggle').prop('disabled', true);

        $.ajax({
            url: '{{ route("settings.toggle-mode") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    updateSettingsDisplay(response.current_mode, response.is_live);
                    
                    // Show success message
                    showSettingsAlert('success', response.message);
                    
                    // Reload page after delay to show updated settings
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showSettingsAlert('error', response.message);
                    // Revert toggle state
                    $('#settingsModeToggle').prop('checked', !$('#settingsModeToggle').prop('checked'));
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to toggle mode';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showSettingsAlert('error', errorMessage);
                // Revert toggle state
                $('#settingsModeToggle').prop('checked', !$('#settingsModeToggle').prop('checked'));
            },
            complete: function() {
                $('#settingsModeToggle').prop('disabled', false);
            }
        });
    }

    function showSettingsAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <strong>${type === 'success' ? 'Success!' : 'Error!'}</strong> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        
        $('.card-body').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
</script>
@endpush
