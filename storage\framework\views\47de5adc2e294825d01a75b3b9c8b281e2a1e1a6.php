<?php $__env->startSection('title','Delegates - DynamoDB'); ?>

<?php $__env->startSection('main-content'); ?>
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            <?php echo $__env->make('backend.layouts.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Delegates (<?php echo e($count); ?> items)</h6>
        <a href="<?php echo e(route('dynamodb.dashboard')); ?>" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Back to Dashboard">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <?php if(count($delegates) > 0): ?>
                <table class="table table-bordered" id="delegate-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Delegate Info</th>
                            <th>Owner</th>
                            <th>Delegation Status</th>
                            <th>User Info</th>
                            <th>Profile Pictures</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $delegates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $delegate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="clickable-row" data-delegate-id="<?php echo e($delegate['id'] ?? ''); ?>">
                            <td><?php echo e($index + 1); ?></td>
                            <td>
                                <div class="delegate-info">
                                    <strong><?php echo e($delegate['delegateName'] ?? $delegate['name'] ?? 'N/A'); ?></strong>
                                    <?php if(isset($delegate['delegateUserID'])): ?>
                                        <br><small class="text-muted">ID: <code><?php echo e(substr($delegate['delegateUserID'], 0, 15)); ?>...</code></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo e($delegate['owner'] ?? 'N/A'); ?></strong>
                                <?php if(isset($delegate['userID'])): ?>
                                    <br><small class="text-muted">User: <code><?php echo e(substr($delegate['userID'], 0, 15)); ?>...</code></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($delegate['delegationStatus'])): ?>
                                    <?php switch($delegate['delegationStatus']):
                                        case ('ACTIVE'): ?>
                                            <span class="badge badge-success"><i class="fas fa-check-circle"></i> <?php echo e($delegate['delegationStatus']); ?></span>
                                            <?php break; ?>
                                        <?php case ('INACTIVE'): ?>
                                            <span class="badge badge-danger"><i class="fas fa-times-circle"></i> <?php echo e($delegate['delegationStatus']); ?></span>
                                            <?php break; ?>
                                        <?php case ('PENDING'): ?>
                                            <span class="badge badge-warning"><i class="fas fa-clock"></i> <?php echo e($delegate['delegationStatus']); ?></span>
                                            <?php break; ?>
                                        <?php case ('SUSPENDED'): ?>
                                            <span class="badge badge-secondary"><i class="fas fa-pause-circle"></i> <?php echo e($delegate['delegationStatus']); ?></span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-info"><?php echo e($delegate['delegationStatus']); ?></span>
                                    <?php endswitch; ?>
                                <?php else: ?>
                                    <span class="badge badge-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="user-info">
                                    <strong><?php echo e($delegate['name'] ?? 'N/A'); ?></strong>
                                    <?php if(isset($delegate['userID'])): ?>
                                        <br><small class="text-muted">ID: <code><?php echo e(substr($delegate['userID'], 0, 15)); ?>...</code></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="profile-pictures">
                                    <?php if(isset($delegate['delegateProfilePicturePath']) && $delegate['delegateProfilePicturePath']): ?>
                                        <span class="badge badge-primary" data-toggle="tooltip" title="Delegate Picture Available">
                                            <i class="fas fa-user-tie"></i> Delegate
                                        </span>
                                    <?php endif; ?>
                                    <?php if(isset($delegate['userProfilePicturePath']) && $delegate['userProfilePicturePath']): ?>
                                        <span class="badge badge-info" data-toggle="tooltip" title="User Picture Available">
                                            <i class="fas fa-user"></i> User
                                        </span>
                                    <?php endif; ?>
                                    <?php if(!isset($delegate['delegateProfilePicturePath']) && !isset($delegate['userProfilePicturePath'])): ?>
                                        <span class="badge badge-secondary">No Pictures</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php echo e(App\Http\Controllers\DynamoDBManagementController::formatDynamoTimestamp($delegate['createdAt'] ?? null)); ?>

                            </td>
                            <td>
                                <button class="btn btn-info btn-sm view-details" data-toggle="tooltip" title="View Details" data-delegate='<?php echo json_encode($delegate, 15, 512) ?>'>
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if(isset($delegate['tempUrl']) && $delegate['tempUrl']): ?>
                                    <a href="<?php echo e($delegate['tempUrl']); ?>" target="_blank" class="btn btn-primary btn-sm" data-toggle="tooltip" title="Open Temp URL">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if(isset($delegate['delegateProfilePicturePath']) && $delegate['delegateProfilePicturePath']): ?>
                                    <button class="btn btn-success btn-sm view-delegate-picture" data-toggle="tooltip" title="View Delegate Picture" data-picture="<?php echo e($delegate['delegateProfilePicturePath']); ?>">
                                        <i class="fas fa-user-tie"></i>
                                    </button>
                                <?php endif; ?>
                                <?php if(isset($delegate['userProfilePicturePath']) && $delegate['userProfilePicturePath']): ?>
                                    <button class="btn btn-warning btn-sm view-user-picture" data-toggle="tooltip" title="View User Picture" data-picture="<?php echo e($delegate['userProfilePicturePath']); ?>">
                                        <i class="fas fa-user"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> No delegates found!</h6>
                    <p>There are no delegates in the DynamoDB table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delegate Details Modal -->
<div class="modal fade" id="delegateModal" tabindex="-1" role="dialog" aria-labelledby="delegateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="delegateModalLabel">Delegate Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="delegateDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Picture Viewer Modal -->
<div class="modal fade" id="pictureModal" tabindex="-1" role="dialog" aria-labelledby="pictureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pictureModalLabel">Profile Picture</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div id="pictureContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet">
<style>
    .table td {
        vertical-align: middle;
    }
    code {
        font-size: 11px;
    }
    .clickable-row {
        cursor: pointer;
    }
    .clickable-row:hover {
        background-color: #f8f9fa;
    }
    .view-details, .view-delegate-picture, .view-user-picture {
        margin-right: 5px;
    }
    .badge {
        font-size: 0.75em;
        margin: 2px;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: 1px solid #dee2e6;
    }
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .modal-body table th {
        background-color: #e9ecef;
        width: 25%;
    }
    .modal-body table td {
        word-break: break-word;
    }
    /* Custom scrollbar for modal */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }
    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }
    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    .btn-sm {
        margin: 1px;
    }
    .delegate-info, .user-info {
        min-height: 40px;
    }
    .profile-pictures {
        min-height: 30px;
    }
    .profile-picture-preview {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .picture-error {
        color: #dc3545;
        font-style: italic;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('backend/vendor/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        $('#delegate-dataTable').DataTable({
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Order by created date
            "columnDefs": [
                {
                    "targets": [5, 7], // Profile Pictures and Actions columns
                    "orderable": false
                }
            ],
            "scrollX": true
        });

        // Handle view details button click
        $('.view-details').on('click', function() {
            var delegate = $(this).data('delegate');
            showDelegateDetails(delegate);
        });

        // Handle view delegate picture button click
        $('.view-delegate-picture').on('click', function() {
            var picture = $(this).data('picture');
            showPicture(picture, 'Delegate Profile Picture');
        });

        // Handle view user picture button click
        $('.view-user-picture').on('click', function() {
            var picture = $(this).data('picture');
            showPicture(picture, 'User Profile Picture');
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });

    function showDelegateDetails(delegate) {
        var detailsHtml = `
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th width="25%">ID:</th>
                            <td><code>${delegate.id || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Type Name:</th>
                            <td><span class="badge badge-info">${delegate.__typename || 'N/A'}</span></td>
                        </tr>
                        <tr>
                            <th>Delegate Name:</th>
                            <td><strong>${delegate.delegateName || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td><strong>${delegate.name || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><strong>${delegate.owner || 'N/A'}</strong></td>
                        </tr>
                        <tr>
                            <th>Delegate User ID:</th>
                            <td><code>${delegate.delegateUserID || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>User ID:</th>
                            <td><code>${delegate.userID || 'N/A'}</code></td>
                        </tr>
                        <tr>
                            <th>Delegation Status:</th>
                            <td>${getDelegationStatusBadge(delegate.delegationStatus)}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>${delegate.createdAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>${delegate.updatedAt || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Delegate Profile Picture:</th>
                            <td>${delegate.delegateProfilePicturePath ? `<a href="${delegate.delegateProfilePicturePath}" target="_blank">View Picture</a>` : '<span class="text-muted">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>User Profile Picture:</th>
                            <td>${delegate.userProfilePicturePath ? `<a href="${delegate.userProfilePicturePath}" target="_blank">View Picture</a>` : '<span class="text-muted">Not Available</span>'}</td>
                        </tr>
                        <tr>
                            <th>Temp URL:</th>
                            <td>${delegate.tempUrl ? `<a href="${delegate.tempUrl}" target="_blank">${delegate.tempUrl}</a>` : '<span class="text-muted">Not Available</span>'}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        $('#delegateDetails').html(detailsHtml);
        $('#delegateModal').modal('show');
    }

    function showPicture(picturePath, title) {
        $('#pictureModalLabel').text(title);

        var pictureHtml = `
            <div class="text-center">
                <img src="${picturePath}" alt="${title}" class="profile-picture-preview"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div class="picture-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> Unable to load image<br>
                    <small>Path: ${picturePath}</small>
                </div>
            </div>
        `;

        $('#pictureContent').html(pictureHtml);
        $('#pictureModal').modal('show');
    }

    function getDelegationStatusBadge(status) {
        if (!status) return '<span class="badge badge-secondary">N/A</span>';

        switch(status) {
            case 'ACTIVE':
                return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> ' + status + '</span>';
            case 'INACTIVE':
                return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> ' + status + '</span>';
            case 'PENDING':
                return '<span class="badge badge-warning"><i class="fas fa-clock"></i> ' + status + '</span>';
            case 'SUSPENDED':
                return '<span class="badge badge-secondary"><i class="fas fa-pause-circle"></i> ' + status + '</span>';
            default:
                return '<span class="badge badge-info">' + status + '</span>';
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\CertHive-Admin-Portal-Siva\resources\views/backend/dynamodb/delegates.blade.php ENDPATH**/ ?>